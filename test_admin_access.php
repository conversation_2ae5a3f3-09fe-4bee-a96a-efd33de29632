<?php
/**
 * Test Admin Access
 * Check if admin functions are working
 */

require_once 'includes/config.php';

echo "<h2>Admin Access Test</h2>";

echo "<h3>Database Connection:</h3>";
if ($conn) {
    echo "✅ Database connected successfully<br>";
} else {
    echo "❌ Database connection failed<br>";
}

echo "<h3>Functions Test:</h3>";

// Test isLoggedIn function
if (function_exists('isLoggedIn')) {
    echo "✅ isLoggedIn function exists<br>";
    echo "Current login status: " . (isLoggedIn() ? "Logged in" : "Not logged in") . "<br>";
} else {
    echo "❌ isLoggedIn function not found<br>";
}

// Test isAdmin function
if (function_exists('isAdmin')) {
    echo "✅ isAdmin function exists<br>";
    echo "Current admin status: " . (isAdmin() ? "Admin" : "Not admin") . "<br>";
} else {
    echo "❌ isAdmin function not found<br>";
}

// Test isPremium function
if (function_exists('isPremium')) {
    echo "✅ isPremium function exists<br>";
    echo "Current premium status: " . (isPremium() ? "Premium" : "Not premium") . "<br>";
} else {
    echo "❌ isPremium function not found<br>";
}

echo "<h3>Database Tables:</h3>";

// Check if download_settings table exists
$table_check = $conn->query("SHOW TABLES LIKE 'download_settings'");
if ($table_check->num_rows > 0) {
    echo "✅ download_settings table exists<br>";
    
    // Get settings
    $settings_query = "SELECT * FROM download_settings";
    $settings_result = $conn->query($settings_query);
    
    if ($settings_result->num_rows > 0) {
        echo "Settings found:<br>";
        while ($row = $settings_result->fetch_assoc()) {
            echo "- " . $row['setting_key'] . ": " . $row['setting_value'] . "<br>";
        }
    } else {
        echo "No settings found in table<br>";
    }
} else {
    echo "❌ download_settings table does not exist<br>";
    echo "<a href='setup_download_simple.php'>Setup Database</a><br>";
}

echo "<h3>Session Info:</h3>";
echo "Session ID: " . session_id() . "<br>";
echo "Session data: <pre>" . print_r($_SESSION, true) . "</pre>";

echo "<h3>Quick Links:</h3>";
echo "<a href='setup_download_simple.php'>Setup Database</a> | ";
echo "<a href='test_download.php'>Test Download</a> | ";
echo "<a href='admin/download_settings.php'>Admin Settings</a> | ";
echo "<a href='login.php'>Login</a>";
?>
