<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Local Player Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body class="bg-dark text-white">
    <div class="container mt-5">
        <h1 class="text-center mb-4">Local Player Test</h1>
        
        <div class="text-center">
            <button class="btn btn-warning btn-lg" onclick="showLocalPlayerOptions('https://example.com/video.mp4', 'Test Movie', 'https://example.com/poster.jpg', '1080p', 'Server 1')">
                <i class="fas fa-play"></i> দেখুন
            </button>
        </div>
    </div>

    <!-- Local Player Selection Modal -->
    <div class="modal fade" id="localPlayerModal" tabindex="-1" aria-labelledby="localPlayerModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content bg-dark text-white">
                <div class="modal-header">
                    <h5 class="modal-title" id="localPlayerModalLabel">
                        <i class="fas fa-play-circle text-warning"></i> লোকাল প্লেয়ারে খুলুন
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="player-options">
                        <div class="player-option" onclick="openInPlayer('vlc')">
                            <div class="player-icon">
                                <i class="fas fa-play-circle" style="color: #ff8800;"></i>
                            </div>
                            <div class="player-info">
                                <h6>VLC Media Player</h6>
                                <small>সবচেয়ে জনপ্রিয় ও নির্ভরযোগ্য প্লেয়ার</small>
                            </div>
                        </div>
                        
                        <div class="player-option mobile-only" onclick="openInPlayer('mx')">
                            <div class="player-icon">
                                <i class="fab fa-android" style="color: #3DDC84;"></i>
                            </div>
                            <div class="player-info">
                                <h6>MX Player</h6>
                                <small>অ্যান্ড্রয়েড ডিভাইসের জন্য সেরা প্লেয়ার</small>
                            </div>
                        </div>
                        
                        <div class="player-option" onclick="openInPlayer('browser')">
                            <div class="player-icon">
                                <i class="fas fa-globe" style="color: #dc3545;"></i>
                            </div>
                            <div class="player-info">
                                <h6>ব্রাউজারে খুলুন</h6>
                                <small>ওয়েব প্লেয়ারে সরাসরি দেখুন</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="direct-link-section mt-3">
                        <label for="directLink" class="form-label">অথবা ডাইরেক্ট লিংক কপি করুন:</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="directLink" readonly>
                            <button class="btn btn-outline-secondary" type="button" onclick="copyDirectLink()">
                                <i class="fas fa-copy"></i> কপি
                            </button>
                        </div>
                        <div class="mt-2 text-center">
                            <button class="btn btn-sm btn-info" type="button" onclick="showPlayerHelp()">
                                <i class="fas fa-question-circle"></i> সাহায্য
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global variables for local player modal
        let currentVideoUrl = '';
        let currentVideoTitle = '';
        let currentVideoPoster = '';
        let currentVideoQuality = '';
        let currentVideoServer = '';

        // Show local player options modal
        function showLocalPlayerOptions(url, title, poster, quality, server) {
            currentVideoUrl = url;
            currentVideoTitle = title;
            currentVideoPoster = poster;
            currentVideoQuality = quality;
            currentVideoServer = server;
            
            // Set the direct link in the input field
            document.getElementById('directLink').value = url;
            
            // Show the modal
            const modal = new bootstrap.Modal(document.getElementById('localPlayerModal'));
            modal.show();
        }

        // Open video in specific player
        function openInPlayer(playerType) {
            let playerUrl = '';
            
            switch(playerType) {
                case 'vlc':
                    playerUrl = 'vlc://' + currentVideoUrl;
                    break;
                case 'mx':
                    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
                    if (isMobile) {
                        playerUrl = 'intent:' + currentVideoUrl + '#Intent;package=com.mxtech.videoplayer.ad;S.title=' + encodeURIComponent(currentVideoTitle) + ';end';
                    } else {
                        alert('MX Player শুধুমাত্র অ্যান্ড্রয়েড ডিভাইসে কাজ করে।');
                        return;
                    }
                    break;
                case 'browser':
                    window.open(currentVideoUrl, '_blank');
                    return;
            }
            
            // Try to open the player URL
            try {
                window.location.href = playerUrl;
            } catch (error) {
                alert('প্লেয়ার খুলতে সমস্যা হয়েছে। ডাইরেক্ট লিংক কপি করে ম্যানুয়ালি খুলুন।');
            }
            
            // Close the modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('localPlayerModal'));
            modal.hide();
        }

        // Copy direct link to clipboard
        function copyDirectLink() {
            const linkInput = document.getElementById('directLink');
            linkInput.select();
            linkInput.setSelectionRange(0, 99999);
            
            try {
                document.execCommand('copy');
                Swal.fire({
                    title: 'সফল!',
                    text: 'লিংক কপি হয়েছে!',
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                });
            } catch (err) {
                alert('লিংক কপি করতে সমস্যা হয়েছে।');
            }
        }

        // Show player help
        function showPlayerHelp() {
            const helpContent = `
                <div style="text-align: left;">
                    <h6>লোকাল প্লেয়ার ব্যবহারের নির্দেশনা:</h6>
                    <ul>
                        <li><strong>VLC Player:</strong> সবচেয়ে নির্ভরযোগ্য, সব ফরম্যাট সাপোর্ট করে</li>
                        <li><strong>MX Player:</strong> অ্যান্ড্রয়েড ডিভাইসের জন্য সেরা</li>
                        <li><strong>ব্রাউজার:</strong> সরাসরি ওয়েব প্লেয়ারে দেখুন</li>
                    </ul>
                    <p><strong>গুরুত্বপূর্ণ:</strong> প্রথমে আপনার পছন্দের প্লেয়ার ইনস্টল করুন।</p>
                </div>
            `;
            
            Swal.fire({
                title: 'প্লেয়ার সাহায্য',
                html: helpContent,
                width: '500px',
                showConfirmButton: true,
                confirmButtonText: 'বুঝেছি'
            });
        }
    </script>

    <style>
        .player-options {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .player-option {
            display: flex;
            align-items: center;
            padding: 15px;
            border: 2px solid #333;
            border-radius: 8px;
            background-color: #1a1a1a;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .player-option:hover {
            border-color: #ff7b00;
            background-color: #2a2a2a;
            transform: translateY(-2px);
        }

        .player-icon {
            font-size: 2rem;
            margin-right: 15px;
            width: 50px;
            text-align: center;
        }

        .player-info h6 {
            margin: 0;
            color: #fff;
            font-weight: 600;
        }

        .player-info small {
            color: #aaa;
            font-size: 0.85rem;
        }

        .direct-link-section .form-label {
            color: #fff;
            font-weight: 500;
        }

        .direct-link-section .form-control {
            background-color: #2a2a2a;
            border-color: #444;
            color: #fff;
        }

        .direct-link-section .form-control:focus {
            background-color: #2a2a2a;
            border-color: #ff7b00;
            color: #fff;
            box-shadow: 0 0 0 0.2rem rgba(255, 123, 0, 0.25);
        }

        @media (max-width: 768px) {
            .desktop-only {
                display: none !important;
            }
        }

        @media (min-width: 769px) {
            .mobile-only {
                display: none !important;
            }
        }
    </style>
</body>
</html>
