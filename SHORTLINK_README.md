# CinePix শর্টলিংক সিস্টেম

একটি সিম্পল এবং কার্যকর URL শর্টেনার সিস্টেম যা আপনার CinePix ওয়েবসাইটের জন্য তৈরি।

## ফিচারসমূহ

### ✅ মূল ফিচার
- **সিম্পল URL শর্টেনিং**: যেকোনো লম্বা URL কে ছোট করুন
- **কাস্টম কোড সাপোর্ট**: নিজের পছন্দের কোড ব্যবহার করুন
- **ক্লিক ট্র্যাকিং**: প্রতিটি লিংকের ক্লিক কাউন্ট দেখুন
- **মেয়াদ শেষ**: লিংকের জন্য মেয়াদ নির্ধারণ করুন
- **সক্রিয়/নিষ্ক্রিয় স্ট্যাটাস**: লিংক চালু/বন্ধ করুন

### 📊 অ্যানালিটিক্স
- মোট লিংক এবং ক্লিক পরিসংখ্যান
- সবচেয়ে জনপ্রিয় লিংক
- সাম্প্রতিক তৈরি লিংক
- গড় ক্লিক রেট

### 🛠️ ম্যানেজমেন্ট
- সহজ লিংক তৈরি এবং এডিট
- বা<PERSON>্ক অপারেশন সাপোর্ট
- সার্চ এবং ফিল্টার
- রেসপন্সিভ ইন্টারফেস

## ইনস্টলেশন

### 1. ডাটাবেস সেটআপ
```sql
-- shortlink_database.sql ফাইল রান করুন
mysql -u username -p database_name < shortlink_database.sql
```

### 2. ফাইল স্ট্রাকচার
```
/
├── shortlink.php                 # মূল রিডাইরেক্ট ফাইল
├── shortlink_manager.php         # ম্যানেজমেন্ট পেজ
├── shortlink_stats.php          # পরিসংখ্যান পেজ
├── api/shortlink.php            # API এন্ডপয়েন্ট
├── includes/
│   ├── shortlink_helper.php     # হেল্পার ফাংশন
│   └── shortlink_widget.php     # উইজেট কম্পোনেন্ট
└── .htaccess                    # URL রিরাইট রুল
```

### 3. URL ফরম্যাট
- **শর্ট URL**: `https://cinepix.top/s/abc123`
- **ম্যানেজমেন্ট**: `https://cinepix.top/shortlink_manager.php`
- **পরিসংখ্যান**: `https://cinepix.top/shortlink_stats.php`

## ব্যবহার

### 🔗 নতুন শর্টলিংক তৈরি

#### ওয়েব ইন্টারফেস দিয়ে:
1. `shortlink_manager.php` পেজে যান
2. "নতুন লিংক তৈরি করুন" বাটনে ক্লিক করুন
3. URL এবং অন্যান্য তথ্য দিন
4. "তৈরি করুন" বাটনে ক্লিক করুন

#### API দিয়ে:
```javascript
fetch('api/shortlink.php', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        url: 'https://example.com/very-long-url',
        title: 'My Link Title',
        custom_code: 'mycode' // ঐচ্ছিক
    })
})
.then(response => response.json())
.then(data => {
    console.log('Short URL:', data.short_url);
});
```

#### PHP ফাংশন দিয়ে:
```php
require_once 'includes/shortlink_helper.php';

$result = createShortLink(
    $conn, 
    'https://example.com/very-long-url',
    'My Link Title',
    'Link description',
    'mycode' // কাস্টম কোড (ঐচ্ছিক)
);

if ($result['success']) {
    echo "Short URL: " . $result['short_url'];
}
```

### 📊 পরিসংখ্যান দেখা

```php
require_once 'includes/shortlink_helper.php';

// মূল পরিসংখ্যান
$stats = getShortLinkStats($conn);
echo "Total Links: " . $stats['total_links'];
echo "Total Clicks: " . $stats['total_clicks'];

// জনপ্রিয় লিংক
$top_links = getTopClickedLinks($conn, 10);
foreach ($top_links as $link) {
    echo $link['short_code'] . " - " . $link['clicks'] . " clicks";
}
```

### 🎬 মুভি/টিভি শোর জন্য অটো শর্টলিংক

```php
// মুভির জন্য
$movie_shortlink = createMovieShortLink($conn, $movie_id, $movie_title, $year);

// টিভি শোর জন্য  
$tvshow_shortlink = createTvShowShortLink($conn, $tvshow_id, $tvshow_title, $year);
```

## API এন্ডপয়েন্ট

### POST `/api/shortlink.php`
নতুন শর্টলিংক তৈরি করুন

**Request Body:**
```json
{
    "url": "https://example.com/long-url",
    "title": "Optional title",
    "description": "Optional description",
    "custom_code": "optional_code",
    "expires_at": "2024-12-31 23:59:59"
}
```

### GET `/api/shortlink.php?code=abc123`
নির্দিষ্ট শর্টলিংকের তথ্য

### GET `/api/shortlink.php?page=1&limit=20`
সব শর্টলিংকের তালিকা

### PUT `/api/shortlink.php`
শর্টলিংক আপডেট করুন

### DELETE `/api/shortlink.php`
শর্টলিংক ডিলিট করুন

## উইজেট ব্যবহার

যেকোনো পেজে শর্টলিংক জেনারেটর যোগ করতে:

```php
<?php include 'includes/shortlink_widget.php'; ?>
```

## কনফিগারেশন

### ডাটাবেস টেবিল স্ট্রাকচার
```sql
CREATE TABLE shortlinks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    short_code VARCHAR(10) NOT NULL UNIQUE,
    original_url TEXT NOT NULL,
    title VARCHAR(255) DEFAULT NULL,
    description TEXT DEFAULT NULL,
    clicks INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    expires_at DATETIME DEFAULT NULL,
    created_by INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### .htaccess রুল
```apache
# Short Link URLs
# /s/code -> shortlink.php?code=code
RewriteRule ^s/([a-zA-Z0-9]{3,10})/?$ shortlink.php?code=$1 [L,QSA]
```

## নিরাপত্তা

- ✅ SQL Injection প্রতিরোধ (Prepared Statements)
- ✅ XSS প্রতিরোধ (Input Sanitization)
- ✅ URL Validation
- ✅ Rate Limiting (প্রয়োজনে যোগ করা যাবে)

## কাস্টমাইজেশন

### কোড দৈর্ঘ্য পরিবর্তন
```php
// shortlink_helper.php এ
function generateShortCode($length = 8) { // 6 থেকে 8 করুন
```

### কাস্টম ডোমেইন
```php
// config.php এ SITE_URL পরিবর্তন করুন
define('SITE_URL', 'https://yourdomain.com');
```

## ট্রাবলশুটিং

### সাধারণ সমস্যা

1. **404 Error**: .htaccess রুল চেক করুন
2. **Database Error**: connection এবং table structure চেক করুন
3. **Permission Error**: ফাইল permission চেক করুন

### লগ চেক করা
```php
// Error logging enable করুন
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## আপগ্রেড

ভবিষ্যতে নতুন ফিচার যোগ করার জন্য:

1. **QR Code Generation**
2. **Bulk Import/Export**
3. **Advanced Analytics**
4. **User-based Links**
5. **API Rate Limiting**

## সাপোর্ট

কোন সমস্যা হলে:
1. Database connection চেক করুন
2. File permissions চেক করুন  
3. Error logs দেখুন
4. API response চেক করুন

---

**তৈরি করেছেন**: CinePix Development Team  
**সর্বশেষ আপডেট**: ২০২৪
