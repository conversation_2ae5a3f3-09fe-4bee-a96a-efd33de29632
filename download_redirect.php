<?php
/**
 * Download Redirect Page with Timer and Ads
 * Shows countdown timer and ads before redirecting to actual download link
 */

require_once 'includes/config.php';
require_once 'includes/auth.php';

// Function to get download settings
function getDownloadSetting($conn, $key, $default = '') {
    $stmt = $conn->prepare("SELECT setting_value FROM download_settings WHERE setting_key = ?");
    $stmt->bind_param("s", $key);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        return $result->fetch_assoc()['setting_value'];
    }

    return $default;
}

// Get parameters
$download_url = $_GET['url'] ?? '';
$title = $_GET['title'] ?? 'Download';
$quality = $_GET['quality'] ?? '';
$server = $_GET['server'] ?? '';

// Validate download URL
if (empty($download_url) || !filter_var($download_url, FILTER_VALIDATE_URL)) {
    header('Location: ' . SITE_URL);
    exit;
}

// Decode URL if it's encoded
$download_url = urldecode($download_url);

// Get settings from database
$default_timer = intval(getDownloadSetting($conn, 'download_timer', 4));
$premium_timer = intval(getDownloadSetting($conn, 'premium_timer', 2));
$show_ads = intval(getDownloadSetting($conn, 'show_ads', 1));
$popunder_ads = intval(getDownloadSetting($conn, 'popunder_ads', 1));
$ad_delay = intval(getDownloadSetting($conn, 'ad_delay', 2));

// Check if user is premium
$is_premium = isLoggedIn() && isPremium();

// Set timer based on user type
$timer = $is_premium ? $premium_timer : $default_timer;

// Set timer limits
$timer = max(1, min(10, $timer));

$page_title = "ডাউনলোড প্রস্তুত হচ্ছে...";
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - CinePix</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .download-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .download-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        
        .download-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .countdown-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: conic-gradient(#667eea 0deg, #e9ecef 0deg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 30px auto;
            position: relative;
            transition: background 0.1s ease;
        }
        
        .countdown-inner {
            width: 100px;
            height: 100px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .download-info {
            margin: 20px 0;
        }
        
        .download-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        
        .download-details {
            color: #666;
            font-size: 0.9rem;
        }
        
        .progress-bar-container {
            margin: 20px 0;
        }
        
        .progress {
            height: 8px;
            border-radius: 10px;
            background-color: #e9ecef;
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 10px;
            transition: width 0.1s ease;
        }
        
        .status-text {
            margin: 15px 0;
            color: #666;
            font-size: 0.9rem;
        }
        
        .ad-container {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 1px dashed #dee2e6;
        }
        
        .skip-button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            cursor: not-allowed;
            opacity: 0.6;
            transition: all 0.3s ease;
        }
        
        .skip-button.active {
            cursor: pointer;
            opacity: 1;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .skip-button.active:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }
        
        .premium-badge {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #333;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-bottom: 15px;
            display: inline-block;
        }
        
        @media (max-width: 768px) {
            .download-card {
                padding: 30px 20px;
                margin: 10px;
            }
            
            .download-icon {
                font-size: 3rem;
            }
            
            .countdown-circle {
                width: 100px;
                height: 100px;
            }
            
            .countdown-inner {
                width: 80px;
                height: 80px;
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="download-container">
        <div class="download-card">
            <?php if ($is_premium): ?>
                <div class="premium-badge">
                    <i class="fas fa-crown"></i> প্রিমিয়াম ইউজার
                </div>
            <?php endif; ?>
            
            <div class="download-icon">
                <i class="fas fa-download"></i>
            </div>
            
            <h2>ডাউনলোড প্রস্তুত হচ্ছে...</h2>
            
            <div class="download-info">
                <div class="download-title"><?php echo htmlspecialchars($title); ?></div>
                <div class="download-details">
                    <?php if ($quality): ?>
                        <span class="badge bg-primary me-2"><?php echo htmlspecialchars($quality); ?></span>
                    <?php endif; ?>
                    <?php if ($server): ?>
                        <span class="badge bg-secondary"><?php echo htmlspecialchars($server); ?></span>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="countdown-circle" id="countdownCircle">
                <div class="countdown-inner">
                    <span id="countdownNumber"><?php echo $timer; ?></span>
                </div>
            </div>
            
            <div class="progress-bar-container">
                <div class="progress">
                    <div class="progress-bar" id="progressBar" style="width: 0%"></div>
                </div>
            </div>
            
            <div class="status-text" id="statusText">
                অনুগ্রহ করে <span id="remainingTime"><?php echo $timer; ?></span> সেকেন্ড অপেক্ষা করুন...
            </div>
            
            <!-- Ad Container (only for non-premium users) -->
            <?php if (!$is_premium && $show_ads): ?>
            <div class="ad-container">
                <div class="text-muted mb-2">
                    <small><i class="fas fa-info-circle"></i> বিজ্ঞাপন</small>
                </div>

                <!-- Banner Ad -->
                <script type="text/javascript">
                    atOptions = {
                        'key' : '735a559a5872816da47237a603cac4ad',
                        'format' : 'iframe',
                        'height' : 90,
                        'width' : 728,
                        'params' : {}
                    };
                    document.write('<scr' + 'ipt type="text/javascript" src="//www.topcreativeformat.com/735a559a5872816da47237a603cac4ad/invoke.js"></scr' + 'ipt>');
                </script>

                <!-- Square Ad -->
                <div class="mt-3">
                    <script type="text/javascript">
                        atOptions = {
                            'key' : 'your-300x250-ad-key',
                            'format' : 'iframe',
                            'height' : 250,
                            'width' : 300,
                            'params' : {}
                        };
                        document.write('<scr' + 'ipt type="text/javascript" src="//www.topcreativeformat.com/your-300x250-ad-key/invoke.js"></scr' + 'ipt>');
                    </script>
                </div>
            </div>
            <?php endif; ?>
            
            <button class="skip-button" id="skipButton" onclick="startDownload()">
                <i class="fas fa-download me-2"></i>
                <span id="buttonText">ডাউনলোড শুরু করুন</span>
            </button>
            
            <div class="mt-3">
                <small class="text-muted">
                    <i class="fas fa-shield-alt"></i> নিরাপদ এবং দ্রুত ডাউনলোড
                </small>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let timeLeft = <?php echo $timer; ?>;
        const totalTime = <?php echo $timer; ?>;
        const downloadUrl = <?php echo json_encode($download_url); ?>;
        
        const countdownNumber = document.getElementById('countdownNumber');
        const countdownCircle = document.getElementById('countdownCircle');
        const progressBar = document.getElementById('progressBar');
        const statusText = document.getElementById('statusText');
        const remainingTime = document.getElementById('remainingTime');
        const skipButton = document.getElementById('skipButton');
        const buttonText = document.getElementById('buttonText');
        
        function updateCountdown() {
            countdownNumber.textContent = timeLeft;
            remainingTime.textContent = timeLeft;
            
            // Update progress bar
            const progress = ((totalTime - timeLeft) / totalTime) * 100;
            progressBar.style.width = progress + '%';
            
            // Update circular progress
            const degrees = (progress / 100) * 360;
            countdownCircle.style.background = `conic-gradient(#667eea ${degrees}deg, #e9ecef ${degrees}deg)`;
            
            if (timeLeft <= 0) {
                // Enable download button
                skipButton.classList.add('active');
                skipButton.style.cursor = 'pointer';
                skipButton.style.opacity = '1';
                buttonText.innerHTML = '<i class="fas fa-download me-2"></i>এখনই ডাউনলোড করুন';
                statusText.innerHTML = '<i class="fas fa-check-circle text-success"></i> ডাউনলোড প্রস্তুত!';
                
                // Auto redirect after 1 more second
                setTimeout(() => {
                    startDownload();
                }, 1000);
                
                return;
            }
            
            timeLeft--;
            setTimeout(updateCountdown, 1000);
        }
        
        function startDownload() {
            if (timeLeft > 0) {
                return; // Don't allow early download
            }
            
            // Show downloading status
            buttonText.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>ডাউনলোড শুরু হচ্ছে...';
            statusText.innerHTML = '<i class="fas fa-download text-primary"></i> ডাউনলোড শুরু হয়েছে...';
            
            // Start download
            window.location.href = downloadUrl;
            
            // Show success message after a delay
            setTimeout(() => {
                statusText.innerHTML = '<i class="fas fa-check-circle text-success"></i> ডাউনলোড সম্পন্ন!';
                buttonText.innerHTML = '<i class="fas fa-check me-2"></i>ডাউনলোড সম্পন্ন';
            }, 2000);
        }
        
        // Start countdown
        updateCountdown();
        
        // Prevent right-click and some keyboard shortcuts
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });
        
        document.addEventListener('keydown', function(e) {
            // Disable F12, Ctrl+Shift+I, Ctrl+U
            if (e.key === 'F12' || 
                (e.ctrlKey && e.shiftKey && e.key === 'I') || 
                (e.ctrlKey && e.key === 'u')) {
                e.preventDefault();
            }
        });
        
        // Show popunder ad for non-premium users
        <?php if (!$is_premium && $popunder_ads): ?>
        setTimeout(() => {
            // Popunder ad code here
            // This will show a popunder ad after configured delay
            const popunder = window.open('', '_blank');
            if (popunder) {
                popunder.document.write(`
                    <html>
                        <head><title>CinePix - Premium Content</title></head>
                        <body style="margin:0; padding:20px; font-family: Arial;">
                            <div style="text-align: center;">
                                <h2>🎬 CinePix Premium</h2>
                                <p>আরও ভালো অভিজ্ঞতার জন্য প্রিমিয়াম সদস্য হন!</p>
                                <a href="<?php echo SITE_URL; ?>/premium.php" style="background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">প্রিমিয়াম হন</a>
                            </div>
                            <script>
                                setTimeout(() => {
                                    window.close();
                                }, 4000);
                            </script>
                        </body>
                    </html>
                `);
                popunder.blur();
                window.focus();
            }
        }, <?php echo $ad_delay * 1000; ?>);
        <?php endif; ?>
    </script>
</body>
</html>
