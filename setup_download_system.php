<?php
/**
 * Setup Download Redirect System
 * Run this file once to setup the database tables and default settings
 */

require_once 'includes/config.php';

// Check if user is admin (optional - remove this check if you want to run it directly)
if (!isLoggedIn() || !isAdmin()) {
    echo "Only admin can run this setup script.";
    exit;
}

$success_messages = [];
$error_messages = [];

// Create download_settings table
$create_settings_table = "CREATE TABLE IF NOT EXISTS download_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(50) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($conn->query($create_settings_table)) {
    $success_messages[] = "download_settings table created successfully";
} else {
    $error_messages[] = "Error creating download_settings table: " . $conn->error;
}

// Insert default settings
$default_settings = [
    'download_timer' => '4',
    'premium_timer' => '2', 
    'show_ads' => '1',
    'popunder_ads' => '1',
    'ad_delay' => '2'
];

foreach ($default_settings as $key => $value) {
    $insert_setting = "INSERT INTO download_settings (setting_key, setting_value) VALUES (?, ?) 
                      ON DUPLICATE KEY UPDATE setting_value = ?";
    $stmt = $conn->prepare($insert_setting);
    $stmt->bind_param("sss", $key, $value, $value);
    
    if ($stmt->execute()) {
        $success_messages[] = "Setting '$key' set to '$value'";
    } else {
        $error_messages[] = "Error setting '$key': " . $stmt->error;
    }
}

// Create shortlinks table (if not exists from previous setup)
$create_shortlinks_table = "CREATE TABLE IF NOT EXISTS shortlinks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    short_code VARCHAR(10) NOT NULL UNIQUE,
    original_url TEXT NOT NULL,
    title VARCHAR(255) DEFAULT NULL,
    description TEXT DEFAULT NULL,
    clicks INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    expires_at DATETIME DEFAULT NULL,
    created_by INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_short_code (short_code),
    INDEX idx_created_by (created_by),
    INDEX idx_active (is_active),
    INDEX idx_expires (expires_at)
)";

if ($conn->query($create_shortlinks_table)) {
    $success_messages[] = "shortlinks table created successfully";
} else {
    $error_messages[] = "Error creating shortlinks table: " . $conn->error;
}

?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ডাউনলোড সিস্টেম সেটআপ - CinePix</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3><i class="fas fa-cog"></i> ডাউনলোড সিস্টেম সেটআপ</h3>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($success_messages)): ?>
                        <div class="alert alert-success">
                            <h5><i class="fas fa-check-circle"></i> সফল!</h5>
                            <ul class="mb-0">
                                <?php foreach ($success_messages as $message): ?>
                                <li><?php echo htmlspecialchars($message); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        <?php endif; ?>

                        <?php if (!empty($error_messages)): ?>
                        <div class="alert alert-danger">
                            <h5><i class="fas fa-exclamation-triangle"></i> ত্রুটি!</h5>
                            <ul class="mb-0">
                                <?php foreach ($error_messages as $message): ?>
                                <li><?php echo htmlspecialchars($message); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        <?php endif; ?>

                        <div class="mt-4">
                            <h5>সেটআপ সম্পন্ন!</h5>
                            <p>আপনার ডাউনলোড রিডাইরেক্ট সিস্টেম এখন ব্যবহারের জন্য প্রস্তুত।</p>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6><i class="fas fa-download"></i> টেস্ট করুন</h6>
                                            <p class="small">ডাউনলোড রিডাইরেক্ট সিস্টেম টেস্ট করুন</p>
                                            <a href="test_download.php" class="btn btn-primary btn-sm" target="_blank">
                                                টেস্ট পেজ
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6><i class="fas fa-cog"></i> সেটিংস</h6>
                                            <p class="small">টাইমার এবং বিজ্ঞাপন সেটিংস পরিবর্তন করুন</p>
                                            <a href="admin/download_settings.php" class="btn btn-success btn-sm" target="_blank">
                                                সেটিংস পেজ
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <div class="card bg-warning bg-opacity-10 border-warning">
                                    <div class="card-body">
                                        <h6><i class="fas fa-info-circle text-warning"></i> গুরুত্বপূর্ণ তথ্য</h6>
                                        <ul class="small mb-0">
                                            <li>এই সেটআপ স্ক্রিপ্টটি শুধুমাত্র একবার চালান</li>
                                            <li>সব ডাউনলোড লিংক এখন রিডাইরেক্ট পেজ দিয়ে যাবে</li>
                                            <li>প্রিমিয়াম ইউজাররা কম টাইমার এবং কোন বিজ্ঞাপন দেখবেন না</li>
                                            <li>সেটিংস পরিবর্তন করতে অ্যাডমিন প্যানেল ব্যবহার করুন</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer text-center">
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-home"></i> হোমে ফিরুন
                        </a>
                        <a href="admin/download_settings.php" class="btn btn-primary ms-2">
                            <i class="fas fa-cog"></i> সেটিংস
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
