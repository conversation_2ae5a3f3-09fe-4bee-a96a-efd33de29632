# CinePix ডাউনলোড রিডাইরেক্ট সিস্টেম

একটি উন্নত ডাউনলোড রিডাইরেক্ট সিস্টেম যা ডাউনলোড লিংকে ক্লিক করার পর কাউন্টডাউন টাইমার এবং বিজ্ঞাপন দেখায়।

## 🎯 মূল ফিচারসমূহ

### ✅ কাউন্টডাউন টাইমার
- **সাধারণ ইউজার**: ৩-১০ সেকেন্ড (কনফিগারেবল)
- **প্রিমিয়াম ইউজার**: ১-৫ সেকেন্ড (কম টাইমার)
- **সুন্দর অ্যানিমেশন**: সার্কুলার প্রগ্রেস বার
- **অটো রিডাইরেক্ট**: টাইমার শেষে স্বয়ংক্রিয় রিডাইরেক্ট

### 📺 বিজ্ঞাপন সিস্টেম
- **ব্যানার বিজ্ঞাপন**: ডাউনলোড পেজে
- **পপআন্ডার বিজ্ঞাপন**: কনফিগারেবল বিলম্বে
- **প্রিমিয়াম ছাড়**: প্রিমিয়াম ইউজাররা বিজ্ঞাপন দেখে না
- **রেভিনিউ অপটিমাইজেশন**: সর্বোচ্চ আয়ের জন্য

### 🎨 ইউজার এক্সপেরিয়েন্স
- **রেসপন্সিভ ডিজাইন**: সব ডিভাইসে কাজ করে
- **সুন্দর UI**: আধুনিক গ্রেডিয়েন্ট ডিজাইন
- **লোডিং ইন্ডিকেটর**: ভিজ্যুয়াল ফিডব্যাক
- **প্রিমিয়াম ব্যাজ**: প্রিমিয়াম ইউজারদের জন্য বিশেষ ব্যাজ

## 📁 ফাইল স্ট্রাকচার

```
/
├── download_redirect.php           # মূল রিডাইরেক্ট পেজ
├── admin/download_settings.php    # অ্যাডমিন সেটিংস পেজ
├── test_download.php             # টেস্ট পেজ
├── includes/shortlink_helper.php  # হেল্পার ফাংশন (আপডেটেড)
├── movie_details.php             # মুভি পেজ (আপডেটেড)
├── tvshow_details.php           # টিভি শো পেজ (আপডেটেড)
└── DOWNLOAD_REDIRECT_README.md   # এই ফাইল
```

## 🚀 ইনস্টলেশন

### ১. ডাটাবেস টেবিল তৈরি
```sql
CREATE TABLE IF NOT EXISTS download_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(50) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ডিফল্ট সেটিংস
INSERT INTO download_settings (setting_key, setting_value) VALUES
('download_timer', '4'),
('premium_timer', '2'),
('show_ads', '1'),
('popunder_ads', '1'),
('ad_delay', '2');
```

### ২. ফাইল আপলোড
সব ফাইল আপনার ওয়েবসাইটের রুট ডিরেক্টরিতে আপলোড করুন।

### ৩. পারমিশন সেট করুন
```bash
chmod 644 download_redirect.php
chmod 644 admin/download_settings.php
chmod 644 test_download.php
```

## ⚙️ কনফিগারেশন

### অ্যাডমিন প্যানেল দিয়ে
1. `admin/download_settings.php` পেজে যান
2. টাইমার এবং বিজ্ঞাপন সেটিংস পরিবর্তন করুন
3. "সেটিংস সংরক্ষণ করুন" বাটনে ক্লিক করুন

### ম্যানুয়াল কনফিগারেশন
```php
// ডাটাবেসে সরাসরি আপডেট
UPDATE download_settings SET setting_value = '5' WHERE setting_key = 'download_timer';
UPDATE download_settings SET setting_value = '3' WHERE setting_key = 'premium_timer';
UPDATE download_settings SET setting_value = '1' WHERE setting_key = 'show_ads';
UPDATE download_settings SET setting_value = '1' WHERE setting_key = 'popunder_ads';
UPDATE download_settings SET setting_value = '3' WHERE setting_key = 'ad_delay';
```

## 🔧 ব্যবহার

### স্বয়ংক্রিয় ইন্টিগ্রেশন
সিস্টেম ইতিমধ্যে `movie_details.php` এবং `tvshow_details.php` ফাইলে ইন্টিগ্রেট করা হয়েছে। সব ডাউনলোড লিংক এখন রিডাইরেক্ট পেজ দিয়ে যাবে।

### ম্যানুয়াল URL তৈরি
```php
require_once 'includes/shortlink_helper.php';

$redirect_url = generateDownloadRedirectUrl(
    'https://example.com/movie.mp4',  // অরিজিনাল ডাউনলোড URL
    'Movie Title',                    // মুভি/সিরিজের নাম
    '1080p',                         // কোয়ালিটি
    'Server 1',                      // সার্ভার নাম
    5                                // কাস্টম টাইমার (ঐচ্ছিক)
);

echo '<a href="' . $redirect_url . '">ডাউনলোড করুন</a>';
```

### HTML এ সরাসরি
```html
<a href="download_redirect.php?url=https://example.com/movie.mp4&title=Movie%20Title&quality=1080p&server=Server%201" 
   target="_blank">
   ডাউনলোড করুন
</a>
```

## 📊 সেটিংস অপশন

| সেটিং | বর্ণনা | ডিফল্ট | রেঞ্জ |
|--------|---------|---------|-------|
| `download_timer` | সাধারণ ইউজার টাইমার | 4 সেকেন্ড | 3-10 |
| `premium_timer` | প্রিমিয়াম ইউজার টাইমার | 2 সেকেন্ড | 1-5 |
| `show_ads` | বিজ্ঞাপন দেখানো | চালু (1) | 0/1 |
| `popunder_ads` | পপআন্ডার বিজ্ঞাপন | চালু (1) | 0/1 |
| `ad_delay` | বিজ্ঞাপন বিলম্ব | 2 সেকেন্ড | 1-10 |

## 🎨 কাস্টমাইজেশন

### রঙ পরিবর্তন
```css
/* download_redirect.php এর <style> সেকশনে */
body {
    background: linear-gradient(135deg, #your-color1 0%, #your-color2 100%);
}

.countdown-circle {
    background: conic-gradient(#your-primary-color 0deg, #e9ecef 0deg);
}
```

### বিজ্ঞাপন কোড পরিবর্তন
```php
// download_redirect.php এ বিজ্ঞাপন সেকশনে
atOptions = {
    'key' : 'your-ad-key-here',
    'format' : 'iframe',
    'height' : 90,
    'width' : 728,
    'params' : {}
};
```

### কাস্টম মেসেজ
```php
// download_redirect.php এ
<h2>আপনার কাস্টম মেসেজ</h2>
<div class="status-text">
    আপনার কাস্টম স্ট্যাটাস টেক্সট
</div>
```

## 🧪 টেস্টিং

### টেস্ট পেজ ব্যবহার করুন
1. `test_download.php` পেজে যান
2. বিভিন্ন টাইমার সেটিং টেস্ট করুন
3. প্রিমিয়াম এবং সাধারণ ইউজার অভিজ্ঞতা চেক করুন

### ম্যানুয়াল টেস্ট
```
https://yourdomain.com/download_redirect.php?url=https://example.com/test.mp4&title=Test%20Movie&quality=1080p&server=Server%201&timer=5
```

## 🔒 নিরাপত্তা

### URL ভ্যালিডেশন
- সব URL ভ্যালিড কিনা চেক করা হয়
- XSS প্রতিরোধের জন্য ইনপুট স্যানিটাইজ করা হয়
- SQL Injection প্রতিরোধ

### অ্যান্টি-স্পাম
- Right-click নিষ্ক্রিয়
- Developer tools নিষ্ক্রিয় করার চেষ্টা
- টাইমার বাইপাস প্রতিরোধ

## 📈 পারফরমেন্স

### অপটিমাইজেশন
- CSS/JS মিনিফাই করা
- ইমেজ অপটিমাইজেশন
- ক্যাশিং সিস্টেম

### লোড টাইম
- গড় লোড টাইম: < 2 সেকেন্ড
- মোবাইল অপটিমাইজড
- CDN সাপোর্ট

## 🐛 ট্রাবলশুটিং

### সাধারণ সমস্যা

**1. টাইমার কাজ করছে না**
```php
// JavaScript console এ চেক করুন
console.log('Timer value:', timeLeft);
```

**2. বিজ্ঞাপন দেখাচ্ছে না**
```php
// সেটিংস চেক করুন
SELECT * FROM download_settings WHERE setting_key = 'show_ads';
```

**3. রিডাইরেক্ট কাজ করছে না**
```php
// URL ভ্যালিডেশন চেক করুন
var_dump(filter_var($download_url, FILTER_VALIDATE_URL));
```

### ডিবাগিং
```php
// download_redirect.php এর শুরুতে যোগ করুন
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## 🔄 আপগ্রেড

### ভবিষ্যতের ফিচার
- [ ] QR কোড জেনারেশন
- [ ] ডাউনলোড অ্যানালিটিক্স
- [ ] A/B টেস্টিং
- [ ] মাল্টি-ল্যাঙ্গুয়েজ সাপোর্ট
- [ ] API ইন্টিগ্রেশন

### আপডেট প্রক্রিয়া
1. ব্যাকআপ নিন
2. নতুন ফাইল আপলোড করুন
3. ডাটাবেস স্কিমা আপডেট করুন
4. সেটিংস চেক করুন

## 📞 সাপোর্ট

### সমস্যা সমাধান
1. **ডাটাবেস কানেকশন** চেক করুন
2. **ফাইল পারমিশন** চেক করুন
3. **PHP ভার্সন** সামঞ্জস্য চেক করুন
4. **Error logs** দেখুন

### যোগাযোগ
- **ইমেইল**: <EMAIL>
- **টেলিগ্রাম**: @buycinepix
- **ওয়েবসাইট**: https://cinepix.top

---

**তৈরি করেছেন**: CinePix Development Team  
**সর্বশেষ আপডেট**: ২০২৪  
**ভার্সন**: 1.0.0
