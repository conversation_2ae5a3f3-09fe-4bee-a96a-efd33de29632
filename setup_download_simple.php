<?php
/**
 * Simple Setup for Download Redirect System
 * Run this file once to setup the database tables and default settings
 * No admin check required
 */

require_once 'includes/config.php';

$success_messages = [];
$error_messages = [];

// Create download_settings table
$create_settings_table = "CREATE TABLE IF NOT EXISTS download_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(50) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($conn->query($create_settings_table)) {
    $success_messages[] = "download_settings table created successfully";
} else {
    $error_messages[] = "Error creating download_settings table: " . $conn->error;
}

// Insert default settings
$default_settings = [
    'download_timer' => '4',
    'premium_timer' => '2', 
    'show_ads' => '1',
    'popunder_ads' => '1',
    'ad_delay' => '2'
];

foreach ($default_settings as $key => $value) {
    $insert_setting = "INSERT INTO download_settings (setting_key, setting_value) VALUES (?, ?) 
                      ON DUPLICATE KEY UPDATE setting_value = ?";
    $stmt = $conn->prepare($insert_setting);
    $stmt->bind_param("sss", $key, $value, $value);
    
    if ($stmt->execute()) {
        $success_messages[] = "Setting '$key' set to '$value'";
    } else {
        $error_messages[] = "Error setting '$key': " . $stmt->error;
    }
}

// Create shortlinks table (if not exists from previous setup)
$create_shortlinks_table = "CREATE TABLE IF NOT EXISTS shortlinks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    short_code VARCHAR(10) NOT NULL UNIQUE,
    original_url TEXT NOT NULL,
    title VARCHAR(255) DEFAULT NULL,
    description TEXT DEFAULT NULL,
    clicks INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    expires_at DATETIME DEFAULT NULL,
    created_by INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_short_code (short_code),
    INDEX idx_created_by (created_by),
    INDEX idx_active (is_active),
    INDEX idx_expires (expires_at)
)";

if ($conn->query($create_shortlinks_table)) {
    $success_messages[] = "shortlinks table created successfully";
} else {
    $error_messages[] = "Error creating shortlinks table: " . $conn->error;
}

?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ডাউনলোড সিস্টেম সেটআপ - CinePix</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .setup-container {
            padding: 50px 0;
        }
        
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .card-header {
            border-radius: 15px 15px 0 0 !important;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h3><i class="fas fa-rocket"></i> ডাউনলোড সিস্টেম সেটআপ সম্পন্ন!</h3>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($success_messages)): ?>
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle"></i> সফলভাবে সেটআপ হয়েছে!</h5>
                                <ul class="mb-0">
                                    <?php foreach ($success_messages as $message): ?>
                                    <li><?php echo htmlspecialchars($message); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            <?php endif; ?>

                            <?php if (!empty($error_messages)): ?>
                            <div class="alert alert-danger">
                                <h5><i class="fas fa-exclamation-triangle"></i> কিছু সমস্যা হয়েছে!</h5>
                                <ul class="mb-0">
                                    <?php foreach ($error_messages as $message): ?>
                                    <li><?php echo htmlspecialchars($message); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            <?php endif; ?>

                            <div class="mt-4">
                                <h5><i class="fas fa-info-circle text-primary"></i> এখন কি করবেন?</h5>
                                
                                <div class="row mt-3">
                                    <div class="col-md-4 mb-3">
                                        <div class="card bg-primary text-white">
                                            <div class="card-body text-center">
                                                <i class="fas fa-play fa-2x mb-2"></i>
                                                <h6>টেস্ট করুন</h6>
                                                <p class="small">সিস্টেম কাজ করছে কিনা দেখুন</p>
                                                <a href="test_download.php" class="btn btn-light btn-sm" target="_blank">
                                                    টেস্ট পেজ
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <div class="card bg-success text-white">
                                            <div class="card-body text-center">
                                                <i class="fas fa-cog fa-2x mb-2"></i>
                                                <h6>সেটিংস</h6>
                                                <p class="small">টাইমার ও বিজ্ঞাপন সেট করুন</p>
                                                <a href="admin/download_settings.php" class="btn btn-light btn-sm" target="_blank">
                                                    সেটিংস
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <div class="card bg-info text-white">
                                            <div class="card-body text-center">
                                                <i class="fas fa-link fa-2x mb-2"></i>
                                                <h6>শর্টলিংক</h6>
                                                <p class="small">শর্টলিংক ম্যানেজ করুন</p>
                                                <a href="shortlink_manager.php" class="btn btn-light btn-sm" target="_blank">
                                                    ম্যানেজার
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="alert alert-info mt-4">
                                    <h6><i class="fas fa-lightbulb"></i> কিভাবে কাজ করে:</h6>
                                    <ol class="mb-0">
                                        <li><strong>ইউজার ডাউনলোড লিংকে ক্লিক করে</strong></li>
                                        <li><strong>প্রথমে রিডাইরেক্ট পেজে যায়</strong> (কাউন্টডাউন + বিজ্ঞাপন)</li>
                                        <li><strong>টাইমার শেষ হলে</strong> অরিজিনাল লিংকে রিডাইরেক্ট</li>
                                        <li><strong>প্রিমিয়াম ইউজার</strong> কম টাইমার + কোন বিজ্ঞাপন নেই</li>
                                    </ol>
                                </div>
                                
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle"></i> গুরুত্বপূর্ণ:</h6>
                                    <ul class="mb-0">
                                        <li>এই সেটআপ ফাইলটি <strong>মুছে দিন</strong> বা নাম পরিবর্তন করুন</li>
                                        <li>আপনার বিজ্ঞাপন কোড <code>download_redirect.php</code> ফাইলে আপডেট করুন</li>
                                        <li>সেটিংস পরিবর্তন করতে অ্যাডমিন প্যানেল ব্যবহার করুন</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer text-center bg-light">
                            <a href="index.php" class="btn btn-secondary me-2">
                                <i class="fas fa-home"></i> হোমে ফিরুন
                            </a>
                            <a href="test_download.php" class="btn btn-primary me-2">
                                <i class="fas fa-play"></i> টেস্ট করুন
                            </a>
                            <a href="admin/download_settings.php" class="btn btn-success">
                                <i class="fas fa-cog"></i> সেটিংস
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
