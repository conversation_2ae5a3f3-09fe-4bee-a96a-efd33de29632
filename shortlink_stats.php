<?php
require_once 'includes/config.php';

$page_title = "শর্টলিংক পরিসংখ্যান";
include 'includes/header.php';

// Get statistics
$stats_query = "SELECT 
    COUNT(*) as total_links,
    SUM(clicks) as total_clicks,
    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_links,
    COUNT(CASE WHEN is_active = 0 THEN 1 END) as inactive_links,
    COUNT(CASE WHEN expires_at IS NOT NULL AND expires_at < NOW() THEN 1 END) as expired_links
FROM shortlinks";

$stats_result = $conn->query($stats_query);
$stats = $stats_result->fetch_assoc();

// Get top clicked links
$top_links_query = "SELECT short_code, original_url, title, clicks, created_at 
FROM shortlinks 
WHERE clicks > 0 
ORDER BY clicks DESC 
LIMIT 10";

$top_links_result = $conn->query($top_links_query);

// Get recent links
$recent_links_query = "SELECT short_code, original_url, title, clicks, created_at 
FROM shortlinks 
ORDER BY created_at DESC 
LIMIT 10";

$recent_links_result = $conn->query($recent_links_query);
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-bar"></i> শর্টলিংক পরিসংখ্যান</h2>
                <a href="shortlink_manager.php" class="btn btn-primary">
                    <i class="fas fa-cog"></i> ম্যানেজ করুন
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-2 col-sm-4 col-6 mb-3">
            <div class="card text-center bg-primary text-white">
                <div class="card-body">
                    <i class="fas fa-link fa-2x mb-2"></i>
                    <h4><?php echo number_format($stats['total_links']); ?></h4>
                    <small>মোট লিংক</small>
                </div>
            </div>
        </div>
        <div class="col-md-2 col-sm-4 col-6 mb-3">
            <div class="card text-center bg-success text-white">
                <div class="card-body">
                    <i class="fas fa-mouse-pointer fa-2x mb-2"></i>
                    <h4><?php echo number_format($stats['total_clicks']); ?></h4>
                    <small>মোট ক্লিক</small>
                </div>
            </div>
        </div>
        <div class="col-md-2 col-sm-4 col-6 mb-3">
            <div class="card text-center bg-info text-white">
                <div class="card-body">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h4><?php echo number_format($stats['active_links']); ?></h4>
                    <small>সক্রিয় লিংক</small>
                </div>
            </div>
        </div>
        <div class="col-md-2 col-sm-4 col-6 mb-3">
            <div class="card text-center bg-secondary text-white">
                <div class="card-body">
                    <i class="fas fa-pause-circle fa-2x mb-2"></i>
                    <h4><?php echo number_format($stats['inactive_links']); ?></h4>
                    <small>নিষ্ক্রিয় লিংক</small>
                </div>
            </div>
        </div>
        <div class="col-md-2 col-sm-4 col-6 mb-3">
            <div class="card text-center bg-warning text-white">
                <div class="card-body">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h4><?php echo number_format($stats['expired_links']); ?></h4>
                    <small>মেয়াদোত্তীর্ণ</small>
                </div>
            </div>
        </div>
        <div class="col-md-2 col-sm-4 col-6 mb-3">
            <div class="card text-center bg-dark text-white">
                <div class="card-body">
                    <i class="fas fa-percentage fa-2x mb-2"></i>
                    <h4><?php echo $stats['total_links'] > 0 ? round(($stats['total_clicks'] / $stats['total_links']), 1) : 0; ?></h4>
                    <small>গড় ক্লিক</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Top Clicked Links -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-fire"></i> সবচেয়ে জনপ্রিয় লিংক</h5>
                </div>
                <div class="card-body">
                    <?php if ($top_links_result->num_rows > 0): ?>
                        <div class="list-group list-group-flush">
                            <?php while ($link = $top_links_result->fetch_assoc()): ?>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">
                                            <code><?php echo htmlspecialchars($link['short_code']); ?></code>
                                            <?php if ($link['title']): ?>
                                                - <?php echo htmlspecialchars($link['title']); ?>
                                            <?php endif; ?>
                                        </h6>
                                        <small class="text-muted text-truncate d-block" style="max-width: 300px;">
                                            <?php echo htmlspecialchars($link['original_url']); ?>
                                        </small>
                                    </div>
                                    <span class="badge bg-primary rounded-pill"><?php echo $link['clicks']; ?></span>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    <?php else: ?>
                        <p class="text-muted text-center">এখনো কোন ক্লিক নেই</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Recent Links -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-clock"></i> সাম্প্রতিক লিংক</h5>
                </div>
                <div class="card-body">
                    <?php if ($recent_links_result->num_rows > 0): ?>
                        <div class="list-group list-group-flush">
                            <?php while ($link = $recent_links_result->fetch_assoc()): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1">
                                                <code><?php echo htmlspecialchars($link['short_code']); ?></code>
                                                <?php if ($link['title']): ?>
                                                    - <?php echo htmlspecialchars($link['title']); ?>
                                                <?php endif; ?>
                                            </h6>
                                            <small class="text-muted text-truncate d-block" style="max-width: 300px;">
                                                <?php echo htmlspecialchars($link['original_url']); ?>
                                            </small>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-secondary"><?php echo $link['clicks']; ?> ক্লিক</span>
                                            <br>
                                            <small class="text-muted">
                                                <?php echo date('d M Y', strtotime($link['created_at'])); ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    <?php else: ?>
                        <p class="text-muted text-center">কোন লিংক নেই</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Link Generator -->
    <div class="row">
        <div class="col-12">
            <h4 class="mb-3"><i class="fas fa-plus-circle"></i> নতুন শর্ট লিংক তৈরি করুন</h4>
            <?php include 'includes/shortlink_widget.php'; ?>
        </div>
    </div>
</div>

<style>
.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.list-group-item {
    border-left: none;
    border-right: none;
}

.list-group-item:first-child {
    border-top: none;
}

.list-group-item:last-child {
    border-bottom: none;
}

.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

@media (max-width: 768px) {
    .card-body h4 {
        font-size: 1.5rem;
    }
    
    .list-group-item .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
    }
    
    .list-group-item .text-end {
        margin-top: 0.5rem;
        text-align: left !important;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
