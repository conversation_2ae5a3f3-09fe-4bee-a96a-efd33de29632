import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';
import '../models/download_link.dart';
import '../services/download_manager.dart';
import '../constants/app_constants.dart';

class ExoVideoPlayer extends StatefulWidget {
  final DownloadLink downloadLink;
  final String title;
  final int contentId;
  final String contentType;
  final int? episodeId;
  final int? seasonId;
  final Function(Duration) onPositionChanged;
  final Duration initialPosition;
  final Function()? onNextEpisode; // Added callback for next episode

  const ExoVideoPlayer({
    super.key,
    required this.downloadLink,
    required this.title,
    required this.contentId,
    required this.contentType,
    this.episodeId,
    this.seasonId,
    required this.onPositionChanged,
    required this.initialPosition,
    this.onNextEpisode,
  });

  @override
  State<ExoVideoPlayer> createState() => _ExoVideoPlayerState();
}

class _ExoVideoPlayerState extends State<ExoVideoPlayer>
    with WidgetsBindingObserver {
  // MediaKit player instance
  late final Player _player;
  // Video controller for UI
  late final VideoController _controller;

  // State variables
  bool _isBuffering = true;
  bool _isInitialized = false;
  List<AudioTrack> _audioTracks = [];
  List<SubtitleTrack> _subtitleTracks =
      []; // সাবটাইটেল ট্র্যাক লিস্ট যোগ করা হয়েছে
  int _currentAudioTrack = 0;
  int _currentSubtitleTrack = -1; // -1 মানে কোন সাবটাইটেল নেই
  double _volume = 1.0;
  Timer? _positionUpdateTimer;
  bool _showControls = true;
  Timer? _controlsTimer;
  bool _usingDummyTracks = false; // Flag to indicate we're using dummy tracks

  // Download manager
  final DownloadManager _downloadManager = DownloadManager();
  bool _isDownloading = false;
  bool _isDownloaded = false;

  // Performance monitoring
  DateTime? _loadStartTime;
  bool _hasShownLoadingTime = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Initialize screen brightness
    _initializeBrightness();

    // Initialize the player
    _initializePlayer();

    // Start position update timer for tracking playback position
    _startPositionUpdateTimer();

    // Auto-hide controls after a few seconds
    _startControlsTimer();

    // Check download status and start auto download
    _checkDownloadStatus();
  }

  Future<void> _initializeBrightness() async {
    // We no longer need to initialize brightness as we've removed the brightness control
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _positionUpdateTimer?.cancel();
    _controlsTimer?.cancel();
    _player.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.paused) {
      // App is in background, pause video
      _player.pause();
    } else if (state == AppLifecycleState.resumed) {
      // App is in foreground, resume if it was playing
      if (_player.state.playing) {
        _player.play();
      }
    }
  }

  void _initializePlayer() async {
    // Start performance monitoring
    _loadStartTime = DateTime.now();

    // Create player instance with optimized configuration
    _player = Player(
      configuration: PlayerConfiguration(
        title: 'CinePix Player',
        // Optimized buffer settings - reduced for faster loading
        bufferSize: 16 *
            1024 *
            1024, // 16MB buffer for balance between performance and memory
        logLevel: MPVLogLevel.error, // Reduce logging for better performance
      ),
    );

    // Create video controller
    _controller = VideoController(_player);

    // Add listener for player state changes
    _player.stream.buffering.listen((buffering) {
      if (mounted) {
        setState(() {
          _isBuffering = buffering;
        });
      }
    });

    // Add listener for playback state
    _player.stream.completed.listen((completed) {
      if (completed) {
        // Video playback completed
        if (mounted) {
          setState(() {
            _showControls = true;
          });

          // Show next episode button if this is an episode and onNextEpisode callback is provided
          if (widget.contentType == 'episode' && widget.onNextEpisode != null) {
            _showNextEpisodeDialog();
          }
        }
      }
    });

    // Add listener for error handling with retry mechanism
    _player.stream.error.listen((error) {
      debugPrint('Player error: $error');
      if (mounted) {
        setState(() {
          _isBuffering = false;
        });

        // Show error with retry option
        _showErrorWithRetry(error);
      }
    });

    // Set initial volume
    _player.setVolume(_volume * 100);

    try {
      // Check if offline version exists first for faster loading
      String mediaUrl = widget.downloadLink.url;
      Map<String, String> headers = {
        'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': '*/*',
        'Accept-Encoding':
            'identity', // Disable compression for faster streaming
        'Connection': 'keep-alive',
        'Range': 'bytes=0-', // Enable range requests for better seeking
        'Referer': 'https://cinepix.top/',
        'Cache-Control': 'no-cache',
      };

      // Check for offline version
      final offlineItem = await _downloadManager.getDownloadedItem(
        widget.contentId,
        widget.contentType,
      );

      if (offlineItem != null) {
        final file = File(offlineItem.filePath);
        if (await file.exists()) {
          mediaUrl = 'file://${offlineItem.filePath}';
          headers = {}; // No headers needed for local files

          // Show offline indicator
          if (mounted && context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('অফলাইন ভার্সন চালানো হচ্ছে'),
                duration: const Duration(seconds: 2),
                behavior: SnackBarBehavior.floating,
                margin: const EdgeInsets.only(bottom: 70, left: 20, right: 20),
                backgroundColor: Colors.green,
              ),
            );
          }
        }
      }

      // Open the media with optimized settings
      await _player.open(
        Media(
          mediaUrl,
          httpHeaders: headers,
          extras: {
            'http-referrer': 'https://cinepix.top',
            'force-audio-track-selection': 'true',
            // Optimized performance options
            'cache': 'yes',
            'cache-secs': '60', // Reduced cache time for faster loading
            'demuxer-max-bytes': '50M', // Reduced for faster loading
            'demuxer-max-back-bytes': '25M',
            'network-timeout':
                '15', // Reduced timeout for faster error detection
            'reconnect-streamed': 'yes',
            'reconnect-delay-max': '3',
            'reconnect-at-eof': 'yes',
            'hwdec': 'auto',
            'vo': 'gpu',
            'video-sync': 'audio',
            // Additional optimizations
            'prefetch-playlist': 'yes',
            'stream-buffer-size': '8192',
            'hr-seek': 'yes',
          },
        ),
      );

      // Get available audio tracks after media is loaded
      _player.stream.tracks.listen((tracks) {
        if (mounted) {
          setState(() {
            _audioTracks = tracks.audio;
            _subtitleTracks = tracks.subtitle;
            _isInitialized = true;
          });

          // Show loading time performance
          if (!_hasShownLoadingTime && _loadStartTime != null) {
            final loadTime = DateTime.now().difference(_loadStartTime!);
            debugPrint('Video loaded in: ${loadTime.inMilliseconds}ms');
            _hasShownLoadingTime = true;
          }

          // Try to detect audio tracks with a slight delay to ensure media is fully loaded
          Future.delayed(const Duration(milliseconds: 500), () {
            if (mounted &&
                (_audioTracks.isEmpty ||
                    widget.downloadLink.url.toLowerCase().contains('.mkv'))) {
              _detectAudioTracks();
              _detectSubtitleTracks();
            }
          });
        }
      });

      // Add a delay and manually check for audio tracks again
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted && _audioTracks.isEmpty) {
          _checkAndAddAudioTracks();
        }
        // Also check for subtitle tracks
        if (mounted && _subtitleTracks.isEmpty) {
          _detectSubtitleTracks();
        }
      });

      // Seek to initial position if needed
      if (widget.initialPosition.inSeconds > 0) {
        _showResumeDialog();
      }
    } catch (e) {
      debugPrint('Error initializing player: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('ভিডিও লোড করতে সমস্যা হয়েছে: $e'),
            backgroundColor: Colors.red,
          ),
        );
        setState(() {
          _isInitialized = true; // Set to true to show error UI
        });
      }
    }
  }

  void _startPositionUpdateTimer() {
    _positionUpdateTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (_player.state.playing && !_player.state.completed) {
        widget.onPositionChanged(_player.state.position);
      }
    });
  }

  void _startControlsTimer() {
    _controlsTimer?.cancel();
    _controlsTimer = Timer(const Duration(seconds: 8), () {
      if (mounted && _showControls) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });

    if (_showControls) {
      _startControlsTimer();
    }
  }

  void _showResumeDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('পুনরায় চালু করুন?'),
        content: Text(
            'আপনি ${widget.initialPosition.inMinutes} মিনিট ${widget.initialPosition.inSeconds % 60} সেকেন্ড দেখেছেন। আপনি কি সেখান থেকে দেখা চালিয়ে যেতে চান?'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Start from beginning
              _player.play();
            },
            child: const Text('শুরু থেকে'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Resume from last position
              _player.seek(widget.initialPosition);
              _player.play();
            },
            child: const Text('পুনরায় চালু করুন'),
          ),
        ],
      ),
    );
  }

  void _showNextEpisodeDialog() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black.withAlpha(230),
        title: const Text(
          'পরবর্তী এপিসোড',
          style: TextStyle(color: Colors.white),
        ),
        content: const Text(
          'আপনি কি পরবর্তী এপিসোড দেখতে চান?',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.white70,
            ),
            child: const Text('না'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              if (widget.onNextEpisode != null) {
                widget.onNextEpisode!();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('হ্যাঁ, পরবর্তী এপিসোড'),
          ),
        ],
      ),
    );
  }

  void _showAudioTracksDialog() {
    // Debug log to check if method is being called
    debugPrint('_showAudioTracksDialog called, tracks: ${_audioTracks.length}');

    // If no audio tracks, try to detect tracks first
    if (_audioTracks.isEmpty && !_usingDummyTracks) {
      _detectAudioTracks();
    }

    // Always show the dialog, even with dummy tracks
    // We'll handle the UI to show Bangla/English options

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 0,
          backgroundColor: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withAlpha(230),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.white24, width: 1),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Padding(
                  padding: EdgeInsets.only(bottom: 16),
                  child: Text(
                    'অডিও ট্র্যাক নির্বাচন করুন',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const Divider(color: Colors.white24),
                ConstrainedBox(
                  constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.4,
                  ),
                  child: _audioTracks.isEmpty
                      ? Center(
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(Icons.audio_file,
                                    color: Colors.white70, size: 48),
                                const SizedBox(height: 16),
                                const Text(
                                  'কোন অডিও ট্র্যাক পাওয়া যায়নি',
                                  style: TextStyle(
                                      color: Colors.white, fontSize: 16),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'এই ফাইলে একাধিক অডিও ট্র্যাক নাও থাকতে পারে',
                                  style: TextStyle(
                                      color: Colors.white70, fontSize: 14),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        )
                      : SingleChildScrollView(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              ...List.generate(_audioTracks.length, (index) {
                                final track = _audioTracks[index];
                                final isSelected = _currentAudioTrack == index;
                                String title =
                                    track.title ?? 'অডিও ট্র্যাক ${index + 1}';
                                String language =
                                    track.language ?? 'অজানা ভাষা';
                                if (title.contains('SOUTHFREAK.COM')) {
                                  title = title
                                      .replaceAll('SOUTHFREAK.COM', '')
                                      .trim();
                                }
                                if (track.language == null ||
                                    track.language!.isEmpty) {
                                  if (title.toLowerCase().contains('bangla') ||
                                      title.toLowerCase().contains('bengali') ||
                                      title.toLowerCase().contains('bn') ||
                                      title.contains('বাংলা')) {
                                    language = 'বাংলা';
                                    title = 'বাংলা অডিও';
                                  } else if (title
                                          .toLowerCase()
                                          .contains('english') ||
                                      title.toLowerCase().contains('eng') ||
                                      title.toLowerCase().contains('en')) {
                                    language = 'ইংরেজি';
                                    title = 'ইংরেজি অডিও';
                                  } else if (title
                                      .toLowerCase()
                                      .contains('hin')) {
                                    language = 'হিন্দি';
                                    title = 'হিন্দি অডিও';
                                  }
                                }
                                return ListTile(
                                  title: Text(
                                    title,
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: isSelected
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                    ),
                                  ),
                                  subtitle: Text(
                                    language,
                                    style:
                                        const TextStyle(color: Colors.white70),
                                  ),
                                  leading: Icon(
                                    isSelected
                                        ? Icons.radio_button_checked
                                        : Icons.radio_button_unchecked,
                                    color: isSelected
                                        ? Colors.blue
                                        : Colors.white54,
                                  ),
                                  selected: isSelected,
                                  onTap: () {
                                    Navigator.of(context).pop();
                                    _switchAudioTrack(index);
                                  },
                                );
                              }),
                              const Divider(color: Colors.white24),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  TextButton(
                                    onPressed: () =>
                                        Navigator.of(context).pop(),
                                    style: TextButton.styleFrom(
                                      foregroundColor: Colors.white,
                                    ),
                                    child: const Text('বাতিল'),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Try to detect audio tracks from the media
  void _detectAudioTracks() {
    debugPrint('Detecting audio tracks from media file');

    // Get current tracks
    final tracks = _player.state.tracks;

    setState(() {
      // If we have real tracks, use them
      if (tracks.audio.isNotEmpty) {
        _audioTracks = tracks.audio;
        _usingDummyTracks = false;

        // Log found tracks for debugging
        debugPrint('Found ${_audioTracks.length} audio tracks:');
        for (var i = 0; i < _audioTracks.length; i++) {
          final track = _audioTracks[i];
          debugPrint(
              'Track $i: ${track.title}, Language: ${track.language}, ID: ${track.id}');
        }

        // Show a message to the user
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  '${_audioTracks.length}টি অডিও ট্র্যাক পাওয়া গেছে। আপনি অডিও আইকনে ক্লিক করে ট্র্যাক পরিবর্তন করতে পারেন।'),
              duration: const Duration(seconds: 3),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        // No audio tracks found
        _audioTracks = [];
        _usingDummyTracks = false;

        // Only show message if it's an MKV file (which should have multiple audio tracks)
        if (widget.downloadLink.url.toLowerCase().contains('.mkv') && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'কোন অডিও ট্র্যাক পাওয়া যায়নি। এটি একটি সমস্যা হতে পারে অথবা ফাইলে একাধিক অডিও ট্র্যাক নাও থাকতে পারে।'),
              duration: Duration(seconds: 3),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    });
  }

  // সাবটাইটেল ট্র্যাক লোড করার মেথড
  void _detectSubtitleTracks() {
    debugPrint('_detectSubtitleTracks called');

    try {
      final tracks = _player.state.tracks;
      debugPrint(
          'Player tracks - audio: ${tracks.audio.length}, subtitle: ${tracks.subtitle.length}');

      setState(() {
        _subtitleTracks = tracks.subtitle;
      });

      debugPrint('Subtitle tracks detected: ${_subtitleTracks.length}');

      if (_subtitleTracks.isNotEmpty) {
        // Log each subtitle track for debugging
        for (var i = 0; i < _subtitleTracks.length; i++) {
          final track = _subtitleTracks[i];
          debugPrint(
              'Subtitle track $i: ${track.title}, Language: ${track.language}, ID: ${track.id}');
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content:
                  Text('${_subtitleTracks.length}টি সাবটাইটেল পাওয়া গেছে'),
              duration: const Duration(seconds: 2),
            ),
          );
        }
      } else {
        debugPrint('No subtitle tracks found in the media file');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('কোন সাবটাইটেল পাওয়া যায়নি'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Error detecting subtitle tracks: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('সাবটাইটেল ডিটেক্ট করতে সমস্যা হয়েছে: $e'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  // সাবটাইটেল ট্র্যাক দেখানোর জন্য নতুন মেথড
  void _showSubtitleTracksDialog() {
    // Debug log to check if method is being called
    debugPrint(
        '_showSubtitleTracksDialog called, subtitle tracks: ${_subtitleTracks.length}');

    // If no subtitle tracks, try to detect tracks first
    if (_subtitleTracks.isEmpty) {
      _detectSubtitleTracks();
    }

    // Always show the dialog, even with no tracks
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.black.withAlpha(230),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.white24, width: 1),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Padding(
                padding: EdgeInsets.only(bottom: 16),
                child: Text(
                  'সাবটাইটেল নির্বাচন করুন',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const Divider(color: Colors.white24),
              ConstrainedBox(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.4,
                ),
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // সাবটাইটেল বন্ধ করার অপশন
                      ListTile(
                        title: const Text(
                          'সাবটাইটেল বন্ধ',
                          style: TextStyle(color: Colors.white),
                        ),
                        leading: Icon(
                          _currentSubtitleTrack == -1
                              ? Icons.radio_button_checked
                              : Icons.radio_button_unchecked,
                          color: _currentSubtitleTrack == -1
                              ? Colors.blue
                              : Colors.white54,
                        ),
                        onTap: () {
                          Navigator.of(context).pop();
                          _switchSubtitleTrack(-1);
                        },
                      ),

                      // সাবটাইটেল ট্র্যাক লিস্ট
                      if (_subtitleTracks.isNotEmpty)
                        ...List.generate(_subtitleTracks.length, (index) {
                          final track = _subtitleTracks[index];
                          final isSelected = _currentSubtitleTrack == index;

                          String title =
                              track.title ?? 'সাবটাইটেল ${index + 1}';
                          String language = track.language ?? 'অজানা ভাষা';

                          return ListTile(
                            title: Text(
                              title,
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: isSelected
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                              ),
                            ),
                            subtitle: Text(
                              language,
                              style: const TextStyle(color: Colors.white70),
                            ),
                            leading: Icon(
                              isSelected
                                  ? Icons.radio_button_checked
                                  : Icons.radio_button_unchecked,
                              color: isSelected ? Colors.blue : Colors.white54,
                            ),
                            onTap: () {
                              Navigator.of(context).pop();
                              _switchSubtitleTrack(index);
                            },
                          );
                        }),

                      if (_subtitleTracks.isEmpty)
                        Center(
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(Icons.subtitles_off,
                                    color: Colors.white70, size: 48),
                                const SizedBox(height: 16),
                                const Text(
                                  'কোন সাবটাইটেল পাওয়া যায়নি',
                                  style: TextStyle(
                                      color: Colors.white, fontSize: 16),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'এই ফাইলে সাবটাইটেল নাও থাকতে পারে',
                                  style: TextStyle(
                                      color: Colors.white70, fontSize: 14),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 16),
                                ElevatedButton(
                                  onPressed: () {
                                    Navigator.of(context).pop();
                                    // Try to detect subtitles again
                                    _detectSubtitleTracks();
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text(
                                            'সাবটাইটেল আবার খোঁজা হচ্ছে...'),
                                        duration: Duration(seconds: 2),
                                      ),
                                    );
                                  },
                                  child: const Text('আবার চেষ্টা করুন'),
                                ),
                              ],
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
              const Divider(color: Colors.white24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('বাতিল'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // সাবটাইটেল ট্র্যাক পরিবর্তন করার মেথড
  void _switchSubtitleTrack(int index) {
    setState(() {
      _currentSubtitleTrack = index;
    });

    if (index == -1) {
      _player.setSubtitleTrack(SubtitleTrack.no());
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('সাবটাইটেল বন্ধ করা হয়েছে'),
          duration: Duration(seconds: 2),
        ),
      );
    } else if (index < _subtitleTracks.length) {
      _player.setSubtitleTrack(_subtitleTracks[index]);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'সাবটাইটেল পরিবর্তন করা হয়েছে: ${_subtitleTracks[index].title ?? 'সাবটাইটেল ${index + 1}'}'),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  // Manually check for audio tracks and add them if needed
  void _checkAndAddAudioTracks() async {
    debugPrint('Manually checking for audio tracks');

    try {
      // Try to get tracks directly from player
      final tracks = _player.state.tracks;

      if (tracks.audio.isNotEmpty) {
        setState(() {
          _audioTracks = tracks.audio;
        });
        debugPrint('Found ${tracks.audio.length} audio tracks on second check');
      } else if (widget.downloadLink.url.toLowerCase().contains('.mkv')) {
        // If still no tracks but it's an MKV file, try to detect tracks
        _detectAudioTracks();
      }
    } catch (e) {
      debugPrint('Error checking audio tracks: $e');
      if (widget.downloadLink.url.toLowerCase().contains('.mkv')) {
        _detectAudioTracks();
      }
    }
  }

  void _switchAudioTrack(int trackIndex) async {
    // Check if we have valid tracks and index
    if (_audioTracks.isEmpty ||
        trackIndex < 0 ||
        trackIndex >= _audioTracks.length) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'অডিও ট্র্যাক পরিবর্তন করা যায়নি: কোন ট্র্যাক পাওয়া যায়নি'),
            duration: Duration(seconds: 2),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    // Update the current track index
    setState(() {
      _currentAudioTrack = trackIndex;
    });

    // Get current position
    final position = _player.state.position;

    // Get the track we're switching to
    final track = _audioTracks[trackIndex];

    // Format track name for display
    String trackName = track.title ?? 'ট্র্যাক ${trackIndex + 1}';

    // Remove domain name from title
    if (trackName.contains('SOUTHFREAK.COM')) {
      trackName = trackName.replaceAll('SOUTHFREAK.COM', '').trim();
    }

    if ((track.language == null || track.language!.isEmpty) &&
        (trackName.toLowerCase().contains('bangla') ||
            trackName.toLowerCase().contains('bengali') ||
            trackName.toLowerCase().contains('bn'))) {
      trackName = 'বাংলা অডিও';
    } else if ((track.language == null || track.language!.isEmpty) &&
        (trackName.toLowerCase().contains('english') ||
            trackName.toLowerCase().contains('eng') ||
            trackName.toLowerCase().contains('en'))) {
      trackName = 'ইংরেজি অডিও';
    } else if ((track.language == null || track.language!.isEmpty) &&
        trackName.toLowerCase().contains('hin')) {
      trackName = 'হিন্দি অডিও';
    }

    try {
      // Select the audio track
      await _player.setAudioTrack(track);

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('অডিও ট্র্যাক পরিবর্তন করা হয়েছে: $trackName'),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error switching audio track: $e');
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('অডিও ট্র্যাক পরিবর্তন করতে সমস্যা হয়েছে: $e'),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.red,
          ),
        );
      }
    }

    // Restore position
    await _player.seek(position);
  }

  void _handleVolumeChange(double value) {
    setState(() {
      _volume = value;
    });
    _player.setVolume(value * 100);
    // Volume indicator is shown on the right side when changing
  }

  void _skipForward() {
    final newPosition = _player.state.position + const Duration(seconds: 10);
    _player.seek(newPosition);
  }

  void _skipBackward() {
    final newPosition = _player.state.position - const Duration(seconds: 10);
    _player.seek(newPosition > Duration.zero ? newPosition : Duration.zero);
  }

  void _showErrorWithRetry(String error) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text(
          'ভিডিও প্লেব্যাক এরর',
          style: TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'ভিডিও চালানোর সময় সমস্যা হয়েছে।',
              style: TextStyle(color: Colors.white70),
            ),
            const SizedBox(height: 8),
            Text(
              'Error: $error',
              style: TextStyle(color: Colors.red[300], fontSize: 12),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Go back to previous screen
            },
            child: const Text(
              'বন্ধ করুন',
              style: TextStyle(color: Colors.grey),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Retry loading
              setState(() {
                _isInitialized = false;
                _isBuffering = true;
              });
              _initializePlayer();
            },
            child: Text(
              'আবার চেষ্টা করুন',
              style: TextStyle(color: AppConstants.primaryColor),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return Container(
        color: Colors.black,
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                valueColor:
                    AlwaysStoppedAnimation<Color>(AppConstants.primaryColor),
                strokeWidth: 3,
              ),
              const SizedBox(height: 16),
              const Text(
                'ভিডিও লোড হচ্ছে...',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
              const SizedBox(height: 8),
              Text(
                'দয়া করে অপেক্ষা করুন',
                style: TextStyle(color: Colors.white70, fontSize: 14),
              ),
            ],
          ),
        ),
      );
    }

    return KeyboardListener(
      focusNode: FocusNode(),
      autofocus: true,
      onKeyEvent: _handleKeyEvent,
      child: GestureDetector(
        onTap: _toggleControls,
        onDoubleTap: () {
          // Double tap to play/pause
          if (_player.state.playing) {
            _player.pause();
          } else {
            _player.play();
          }
          setState(() {});
        },
        // Prevent controls from hiding when interacting with controls
        behavior: HitTestBehavior.translucent,
        child: Stack(
          children: [
            // Video player
            Video(
              controller: _controller,
              controls: NoVideoControls,
              wakelock: true,
              fit: BoxFit.contain,
            ),

            // Buffering indicator
            if (_isBuffering)
              Positioned.fill(
                child: Container(
                  color: Colors.black.withValues(alpha: 0.7),
                  child: Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                              AppConstants.primaryColor),
                          strokeWidth: 3,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'বাফারিং...',
                          style: TextStyle(color: Colors.white, fontSize: 16),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'ভিডিও লোড হচ্ছে',
                          style: TextStyle(color: Colors.white70, fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

            // Custom controls overlay with animation
            AnimatedOpacity(
              opacity: _showControls ? 1.0 : 0.0,
              duration: const Duration(milliseconds: 300),
              child: _showControls
                  ? Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.black.withAlpha(180),
                            Colors.transparent,
                            Colors.transparent,
                            Colors.black.withAlpha(180),
                          ],
                          stops: const [0.0, 0.3, 0.7, 1.0],
                        ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // Top controls - title and audio track selection
                          Container(
                            padding: const EdgeInsets.all(16.0),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  Colors.black.withAlpha(150),
                                  Colors.transparent,
                                ],
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // Back button
                                IconButton(
                                  icon: const Icon(Icons.arrow_back,
                                      color: Colors.white),
                                  onPressed: () {
                                    Navigator.of(context).pop();
                                  },
                                ),
                                // Title
                                Expanded(
                                  child: Text(
                                    widget.title,
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      shadows: [
                                        Shadow(
                                          blurRadius: 4.0,
                                          color: Colors.black,
                                          offset: Offset(1.0, 1.0),
                                        ),
                                      ],
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                                // Removed audio track button from top
                              ],
                            ),
                          ),

                          // Center controls - play/pause, skip backward/forward
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // Skip backward button
                              Container(
                                decoration: BoxDecoration(
                                  color: Colors.black.withAlpha(150),
                                  shape: BoxShape.circle,
                                ),
                                child: IconButton(
                                  iconSize: 56,
                                  icon: const Icon(
                                    Icons.replay_10,
                                    color: Colors.white,
                                    size: 36,
                                  ),
                                  onPressed: _skipBackward,
                                ),
                              ),
                              const SizedBox(width: 24),
                              // Play/Pause button
                              Container(
                                decoration: BoxDecoration(
                                  color: Colors.black.withAlpha(150),
                                  shape: BoxShape.circle,
                                ),
                                child: IconButton(
                                  iconSize: 80,
                                  icon: Icon(
                                    _player.state.playing
                                        ? Icons.pause
                                        : Icons.play_arrow,
                                    color: Colors.white,
                                    size: 56,
                                  ),
                                  onPressed: () {
                                    if (_player.state.playing) {
                                      _player.pause();
                                    } else {
                                      _player.play();
                                    }
                                    setState(() {});
                                  },
                                ),
                              ),
                              const SizedBox(width: 24),
                              // Skip forward button
                              Container(
                                decoration: BoxDecoration(
                                  color: Colors.black.withAlpha(150),
                                  shape: BoxShape.circle,
                                ),
                                child: IconButton(
                                  iconSize: 56,
                                  icon: const Icon(
                                    Icons.forward_10,
                                    color: Colors.white,
                                    size: 36,
                                  ),
                                  onPressed: _skipForward,
                                ),
                              ),
                            ],
                          ),

                          // Bottom controls - progress bar, volume, fullscreen
                          Container(
                            padding: const EdgeInsets.only(bottom: 16),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.bottomCenter,
                                end: Alignment.topCenter,
                                colors: [
                                  Colors.black.withAlpha(180),
                                  Colors.transparent,
                                ],
                              ),
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // Progress bar
                                StreamBuilder<Duration>(
                                  stream: _player.stream.position,
                                  builder: (context, snapshot) {
                                    final position =
                                        snapshot.data ?? Duration.zero;
                                    final duration = _player.state.duration;

                                    // Prevent division by zero
                                    final max = duration.inMilliseconds > 0
                                        ? duration.inMilliseconds.toDouble()
                                        : 1.0;

                                    // Clamp position value to valid range
                                    final value = position.inMilliseconds
                                        .toDouble()
                                        .clamp(0.0, max);

                                    return Column(
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 16),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                _formatDuration(position),
                                                style: const TextStyle(
                                                  color: Colors.white,
                                                  shadows: [
                                                    Shadow(
                                                      blurRadius: 4.0,
                                                      color: Colors.black,
                                                      offset: Offset(1.0, 1.0),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              Text(
                                                _formatDuration(duration),
                                                style: const TextStyle(
                                                  color: Colors.white,
                                                  shadows: [
                                                    Shadow(
                                                      blurRadius: 4.0,
                                                      color: Colors.black,
                                                      offset: Offset(1.0, 1.0),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        SliderTheme(
                                          data: SliderThemeData(
                                            thumbShape:
                                                const RoundSliderThumbShape(
                                              enabledThumbRadius: 8,
                                            ),
                                            overlayShape:
                                                const RoundSliderOverlayShape(
                                              overlayRadius: 16,
                                            ),
                                            trackHeight: 6,
                                            activeTrackColor: Colors.blue,
                                            inactiveTrackColor:
                                                Colors.grey[700],
                                            thumbColor: Colors.white,
                                            overlayColor:
                                                Colors.blue.withAlpha(80),
                                          ),
                                          child: Slider(
                                            value: value,
                                            min: 0,
                                            max: max,
                                            onChanged: (value) {
                                              // Show buffering indicator when seeking
                                              setState(() {
                                                _isBuffering = true;
                                              });

                                              _player.seek(Duration(
                                                milliseconds: value.toInt(),
                                              ));
                                            },
                                          ),
                                        ),
                                      ],
                                    );
                                  },
                                ),

                                // Bottom row with audio, volume and fullscreen buttons
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 8,
                                  ),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      // Audio track button (larger size and improved tap area)
                                      Material(
                                        color: Colors.transparent,
                                        child: InkWell(
                                          borderRadius:
                                              BorderRadius.circular(24),
                                          onTap: () {
                                            debugPrint(
                                                'Audio track button pressed');
                                            // Cancel the auto-hide timer when opening dialog
                                            _controlsTimer?.cancel();
                                            _showAudioTracksDialog();
                                          },
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 16, vertical: 8),
                                            decoration: BoxDecoration(
                                              color:
                                                  Colors.black.withAlpha(150),
                                              borderRadius:
                                                  BorderRadius.circular(24),
                                            ),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                const Icon(
                                                  Icons.audiotrack,
                                                  color: Colors.white,
                                                  size: 24,
                                                ),
                                                const SizedBox(width: 8),
                                                const Text(
                                                  'অডিও',
                                                  style: TextStyle(
                                                    color: Colors.white,
                                                    fontSize: 16,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),

                                      // Right side controls (volume and fullscreen)
                                      Row(
                                        children: [
                                          // Subtitle button - only show if subtitles available or MKV file
                                          if (_subtitleTracks.isNotEmpty ||
                                              widget.downloadLink.url
                                                  .toLowerCase()
                                                  .contains('.mkv'))
                                            Container(
                                              decoration: BoxDecoration(
                                                color:
                                                    Colors.black.withAlpha(100),
                                                borderRadius:
                                                    BorderRadius.circular(20),
                                              ),
                                              child: IconButton(
                                                icon: Icon(
                                                  _subtitleTracks.isNotEmpty
                                                      ? Icons.subtitles
                                                      : Icons.subtitles_off,
                                                  color:
                                                      _subtitleTracks.isNotEmpty
                                                          ? Colors.white
                                                          : Colors.white54,
                                                ),
                                                onPressed: () {
                                                  debugPrint(
                                                      'Subtitle button pressed');
                                                  // Cancel the auto-hide timer when opening dialog
                                                  _controlsTimer?.cancel();
                                                  _showSubtitleTracksDialog();
                                                },
                                              ),
                                            ),

                                          if (_subtitleTracks.isNotEmpty ||
                                              widget.downloadLink.url
                                                  .toLowerCase()
                                                  .contains('.mkv'))
                                            const SizedBox(width: 8),

                                          // Volume button
                                          Container(
                                            decoration: BoxDecoration(
                                              color:
                                                  Colors.black.withAlpha(100),
                                              borderRadius:
                                                  BorderRadius.circular(20),
                                            ),
                                            child: IconButton(
                                              icon: Icon(
                                                _volume <= 0
                                                    ? Icons.volume_off
                                                    : _volume < 0.5
                                                        ? Icons.volume_down
                                                        : Icons.volume_up,
                                                color: Colors.white,
                                              ),
                                              onPressed: () {
                                                // Toggle mute/unmute
                                                if (_volume > 0) {
                                                  _handleVolumeChange(0);
                                                } else {
                                                  _handleVolumeChange(1.0);
                                                }
                                              },
                                            ),
                                          ),

                                          const SizedBox(width: 8),

                                          // Fullscreen button
                                          Container(
                                            decoration: BoxDecoration(
                                              color:
                                                  Colors.black.withAlpha(100),
                                              borderRadius:
                                                  BorderRadius.circular(20),
                                            ),
                                            child: IconButton(
                                              icon: const Icon(
                                                Icons.fullscreen,
                                                color: Colors.white,
                                              ),
                                              onPressed: () {
                                                // Toggle fullscreen
                                                // This is already handled by the video_player_screen.dart
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    )
                  : const SizedBox.shrink(),
            ),

            // Volume control (right side of screen)
            Positioned(
              right: 0,
              top: 0,
              bottom: 0,
              width: MediaQuery.of(context).size.width /
                  3, // One-third screen width
              child: GestureDetector(
                onVerticalDragStart: (details) {
                  // Show controls when starting to drag
                  setState(() {
                    _showControls = true;
                  });
                },
                onVerticalDragUpdate: (details) {
                  // Reverse direction: up increases volume, down decreases
                  final newVolume =
                      (_volume - details.delta.dy * 0.01).clamp(0.0, 1.0);
                  _handleVolumeChange(newVolume);
                },
                onVerticalDragEnd: (details) {
                  // Start timer to hide controls
                  _startControlsTimer();
                },
                child: Container(
                  color: Colors.transparent,
                ),
              ),
            ),

            // Volume indicator (only shown when changing volume via swipe) - smaller size
            Positioned(
              right: 15, // Position on the right side
              top: MediaQuery.of(context).size.height / 2 - 30,
              child: AnimatedOpacity(
                opacity: _showControls ? 1.0 : 0.0,
                duration: const Duration(milliseconds: 300),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withAlpha(180),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.blue, width: 1),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _volume <= 0
                            ? Icons.volume_off
                            : _volume < 0.5
                                ? Icons.volume_down
                                : Icons.volume_up,
                        color: Colors.white,
                        size: 24,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${(_volume * 100).toInt()}%',
                        style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Handle TV remote control keys
  void _handleKeyEvent(KeyEvent event) {
    if (event is! KeyDownEvent) return;

    final key = event.logicalKey;

    // Show controls when any key is pressed
    setState(() {
      _showControls = true;
    });
    _startControlsTimer();

    // Handle different keys
    if (key == LogicalKeyboardKey.select ||
        key == LogicalKeyboardKey.enter ||
        key == LogicalKeyboardKey.space) {
      // Play/Pause
      if (_player.state.playing) {
        _player.pause();
      } else {
        _player.play();
      }
      setState(() {});
    } else if (key == LogicalKeyboardKey.arrowLeft) {
      // Skip backward
      _skipBackward();
    } else if (key == LogicalKeyboardKey.arrowRight) {
      // Skip forward
      _skipForward();
    } else if (key == LogicalKeyboardKey.arrowUp) {
      // Volume up
      final newVolume = (_volume + 0.1).clamp(0.0, 1.0);
      _handleVolumeChange(newVolume);
    } else if (key == LogicalKeyboardKey.arrowDown) {
      // Volume down
      final newVolume = (_volume - 0.1).clamp(0.0, 1.0);
      _handleVolumeChange(newVolume);
    } else if (key == LogicalKeyboardKey.escape ||
        key == LogicalKeyboardKey.goBack) {
      // Back/Exit
      Navigator.of(context).pop();
    } else if (key == LogicalKeyboardKey.contextMenu) {
      // Show audio tracks menu
      _showAudioTracksDialog();
    }
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));

    return duration.inHours > 0
        ? '$hours:$minutes:$seconds'
        : '$minutes:$seconds';
  }

  // Download management methods
  Future<void> _checkDownloadStatus() async {
    _isDownloaded = await _downloadManager.isDownloaded(
      widget.contentId,
      widget.contentType,
    );

    if (mounted) {
      setState(() {});
    }

    // Start auto download if not already downloaded
    if (!_isDownloaded) {
      _startAutoDownload();
    }
  }

  Future<void> _startAutoDownload() async {
    if (_isDownloading) return;

    setState(() {
      _isDownloading = true;
    });

    try {
      final taskId = await _downloadManager.startDownload(
        downloadLink: widget.downloadLink,
        title: widget.title,
        contentId: widget.contentId,
        contentType: widget.contentType,
      );

      if (taskId != null) {
        debugPrint('Started auto download: $taskId');

        // Show subtle notification
        if (mounted && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${widget.title} ডাউনলোড শুরু হয়েছে'),
              duration: const Duration(seconds: 2),
              behavior: SnackBarBehavior.floating,
              margin: const EdgeInsets.only(bottom: 70, left: 20, right: 20),
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Auto download failed: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isDownloading = false;
        });
      }
    }
  }
}
