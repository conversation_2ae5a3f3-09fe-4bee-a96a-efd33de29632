<?php
/**
 * Short Link API
 * Create and manage short links
 */

header('Content-Type: application/json');
require_once '../includes/config.php';

// Function to generate random short code
function generateShortCode($length = 6) {
    $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $code = '';
    for ($i = 0; $i < $length; $i++) {
        $code .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $code;
}

// Function to check if short code exists
function shortCodeExists($conn, $code) {
    $stmt = $conn->prepare("SELECT id FROM shortlinks WHERE short_code = ?");
    $stmt->bind_param("s", $code);
    $stmt->execute();
    return $stmt->get_result()->num_rows > 0;
}

// Function to validate URL
function isValidUrl($url) {
    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'POST':
        // Create new short link
        $input = json_decode(file_get_contents('php://input'), true);
        
        $original_url = $input['url'] ?? '';
        $title = $input['title'] ?? '';
        $description = $input['description'] ?? '';
        $custom_code = $input['custom_code'] ?? '';
        $expires_at = $input['expires_at'] ?? null;
        
        // Validate required fields
        if (empty($original_url)) {
            http_response_code(400);
            echo json_encode(['error' => 'URL is required']);
            exit;
        }
        
        if (!isValidUrl($original_url)) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid URL format']);
            exit;
        }
        
        // Generate or use custom short code
        if (!empty($custom_code)) {
            // Validate custom code
            if (!preg_match('/^[a-zA-Z0-9]{3,10}$/', $custom_code)) {
                http_response_code(400);
                echo json_encode(['error' => 'Custom code must be 3-10 alphanumeric characters']);
                exit;
            }
            
            if (shortCodeExists($conn, $custom_code)) {
                http_response_code(400);
                echo json_encode(['error' => 'Custom code already exists']);
                exit;
            }
            
            $short_code = $custom_code;
        } else {
            // Generate unique short code
            do {
                $short_code = generateShortCode();
            } while (shortCodeExists($conn, $short_code));
        }
        
        // Insert into database
        $stmt = $conn->prepare("INSERT INTO shortlinks (short_code, original_url, title, description, expires_at) VALUES (?, ?, ?, ?, ?)");
        $stmt->bind_param("sssss", $short_code, $original_url, $title, $description, $expires_at);
        
        if ($stmt->execute()) {
            $short_url = SITE_URL . '/s/' . $short_code;
            echo json_encode([
                'success' => true,
                'short_code' => $short_code,
                'short_url' => $short_url,
                'original_url' => $original_url,
                'title' => $title
            ]);
        } else {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to create short link']);
        }
        break;
        
    case 'GET':
        // Get short link info or list all links
        $code = $_GET['code'] ?? '';
        
        if (!empty($code)) {
            // Get specific short link info
            $stmt = $conn->prepare("SELECT * FROM shortlinks WHERE short_code = ?");
            $stmt->bind_param("s", $code);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $link = $result->fetch_assoc();
                $link['short_url'] = SITE_URL . '/s/' . $link['short_code'];
                echo json_encode($link);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Short link not found']);
            }
        } else {
            // List all short links (with pagination)
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = min(50, max(10, intval($_GET['limit'] ?? 20)));
            $offset = ($page - 1) * $limit;
            
            $stmt = $conn->prepare("SELECT * FROM shortlinks ORDER BY created_at DESC LIMIT ? OFFSET ?");
            $stmt->bind_param("ii", $limit, $offset);
            $stmt->execute();
            $result = $stmt->get_result();
            
            $links = [];
            while ($row = $result->fetch_assoc()) {
                $row['short_url'] = SITE_URL . '/s/' . $row['short_code'];
                $links[] = $row;
            }
            
            // Get total count
            $count_result = $conn->query("SELECT COUNT(*) as total FROM shortlinks");
            $total = $count_result->fetch_assoc()['total'];
            
            echo json_encode([
                'links' => $links,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                ]
            ]);
        }
        break;
        
    case 'PUT':
        // Update short link
        $input = json_decode(file_get_contents('php://input'), true);
        $code = $input['code'] ?? '';
        
        if (empty($code)) {
            http_response_code(400);
            echo json_encode(['error' => 'Short code is required']);
            exit;
        }
        
        $updates = [];
        $params = [];
        $types = '';
        
        if (isset($input['title'])) {
            $updates[] = 'title = ?';
            $params[] = $input['title'];
            $types .= 's';
        }
        
        if (isset($input['description'])) {
            $updates[] = 'description = ?';
            $params[] = $input['description'];
            $types .= 's';
        }
        
        if (isset($input['is_active'])) {
            $updates[] = 'is_active = ?';
            $params[] = $input['is_active'] ? 1 : 0;
            $types .= 'i';
        }
        
        if (isset($input['expires_at'])) {
            $updates[] = 'expires_at = ?';
            $params[] = $input['expires_at'];
            $types .= 's';
        }
        
        if (empty($updates)) {
            http_response_code(400);
            echo json_encode(['error' => 'No fields to update']);
            exit;
        }
        
        $params[] = $code;
        $types .= 's';
        
        $sql = "UPDATE shortlinks SET " . implode(', ', $updates) . " WHERE short_code = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param($types, ...$params);
        
        if ($stmt->execute() && $stmt->affected_rows > 0) {
            echo json_encode(['success' => true, 'message' => 'Short link updated']);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'Short link not found or no changes made']);
        }
        break;
        
    case 'DELETE':
        // Delete short link
        $input = json_decode(file_get_contents('php://input'), true);
        $code = $input['code'] ?? '';
        
        if (empty($code)) {
            http_response_code(400);
            echo json_encode(['error' => 'Short code is required']);
            exit;
        }
        
        $stmt = $conn->prepare("DELETE FROM shortlinks WHERE short_code = ?");
        $stmt->bind_param("s", $code);
        
        if ($stmt->execute() && $stmt->affected_rows > 0) {
            echo json_encode(['success' => true, 'message' => 'Short link deleted']);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'Short link not found']);
        }
        break;
        
    default:
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        break;
}
?>
