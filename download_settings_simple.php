<?php
/**
 * Simple Download Settings (No Admin Check)
 * Temporary file for testing download settings without admin login
 */

require_once 'includes/config.php';

// Handle form submission
if ($_POST) {
    $download_timer = intval($_POST['download_timer'] ?? 4);
    $premium_timer = intval($_POST['premium_timer'] ?? 2);
    $show_ads = isset($_POST['show_ads']) ? 1 : 0;
    $popunder_ads = isset($_POST['popunder_ads']) ? 1 : 0;
    $ad_delay = intval($_POST['ad_delay'] ?? 2);
    
    // Validate timer values
    $download_timer = max(3, min(10, $download_timer));
    $premium_timer = max(1, min(5, $premium_timer));
    $ad_delay = max(1, min(10, $ad_delay));
    
    // Save settings to database
    $settings = [
        'download_timer' => $download_timer,
        'premium_timer' => $premium_timer,
        'show_ads' => $show_ads,
        'popunder_ads' => $popunder_ads,
        'ad_delay' => $ad_delay
    ];
    
    // Create settings table if not exists
    $create_table = "CREATE TABLE IF NOT EXISTS download_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(50) NOT NULL UNIQUE,
        setting_value TEXT NOT NULL,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    mysqli_query($conn, $create_table);
    
    // Save each setting
    foreach ($settings as $key => $value) {
        $key_escaped = mysqli_real_escape_string($conn, $key);
        $value_escaped = mysqli_real_escape_string($conn, $value);

        $query = "INSERT INTO download_settings (setting_key, setting_value) VALUES ('$key_escaped', '$value_escaped')
                  ON DUPLICATE KEY UPDATE setting_value = '$value_escaped'";
        $conn->query($query);
    }
    
    $success_message = "সেটিংস সফলভাবে সংরক্ষিত হয়েছে!";
}

// Get current settings
function getSetting($conn, $key, $default = '') {
    // Check if table exists first
    $table_check = $conn->query("SHOW TABLES LIKE 'download_settings'");
    if ($table_check->num_rows == 0) {
        return $default;
    }

    // Use simple query instead of prepared statement for compatibility
    $key_escaped = mysqli_real_escape_string($conn, $key);
    $query = "SELECT setting_value FROM download_settings WHERE setting_key = '$key_escaped'";
    $result = $conn->query($query);

    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        return $row['setting_value'];
    }

    return $default;
}

$current_settings = [
    'download_timer' => getSetting($conn, 'download_timer', 4),
    'premium_timer' => getSetting($conn, 'premium_timer', 2),
    'show_ads' => getSetting($conn, 'show_ads', 1),
    'popunder_ads' => getSetting($conn, 'popunder_ads', 1),
    'ad_delay' => getSetting($conn, 'ad_delay', 2)
];

$page_title = "ডাউনলোড সেটিংস (সিম্পল)";
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - CinePix</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .settings-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            margin-bottom: 20px;
        }
        
        .card-header {
            border-radius: 15px 15px 0 0 !important;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
    </style>
</head>
<body>
    <div class="settings-container">
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-download"></i> <?php echo $page_title; ?></h3>
            </div>
            <div class="card-body">
                <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <form method="POST">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-clock"></i> টাইমার সেটিংস</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label fw-bold">সাধারণ ইউজার টাইমার</label>
                                    <p class="text-muted small">সাধারণ ইউজারদের জন্য ডাউনলোড টাইমার (৩-১০ সেকেন্ড)</p>
                                    <div class="input-group" style="max-width: 200px;">
                                        <input type="number" class="form-control" name="download_timer" 
                                               value="<?php echo $current_settings['download_timer']; ?>" 
                                               min="3" max="10" required>
                                        <span class="input-group-text">সেকেন্ড</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label fw-bold">প্রিমিয়াম ইউজার টাইমার</label>
                                    <p class="text-muted small">প্রিমিয়াম ইউজারদের জন্য কম টাইমার (১-৫ সেকেন্ড)</p>
                                    <div class="input-group" style="max-width: 200px;">
                                        <input type="number" class="form-control" name="premium_timer" 
                                               value="<?php echo $current_settings['premium_timer']; ?>" 
                                               min="1" max="5" required>
                                        <span class="input-group-text">সেকেন্ড</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-ad"></i> বিজ্ঞাপন সেটিংস</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="show_ads" 
                                               <?php echo $current_settings['show_ads'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label fw-bold">বিজ্ঞাপন দেখান</label>
                                    </div>
                                    <p class="text-muted small mt-2">ডাউনলোড পেজে বিজ্ঞাপন দেখানো হবে</p>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="popunder_ads" 
                                               <?php echo $current_settings['popunder_ads'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label fw-bold">পপআন্ডার বিজ্ঞাপন</label>
                                    </div>
                                    <p class="text-muted small mt-2">পপআন্ডার বিজ্ঞাপন সক্রিয় করুন</p>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label fw-bold">বিজ্ঞাপন বিলম্ব</label>
                                    <p class="text-muted small">পপআন্ডার বিজ্ঞাপন কত সেকেন্ড পর দেখাবে</p>
                                    <div class="input-group" style="max-width: 150px;">
                                        <input type="number" class="form-control" name="ad_delay" 
                                               value="<?php echo $current_settings['ad_delay']; ?>" 
                                               min="1" max="10" required>
                                        <span class="input-group-text">সেকেন্ড</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save"></i> সেটিংস সংরক্ষণ করুন
                        </button>
                    </div>
                </form>

                <div class="mt-4">
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-info-circle"></i> গুরুত্বপূর্ণ তথ্য</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i> প্রিমিয়াম ইউজাররা কম টাইমার পাবেন</li>
                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i> প্রিমিয়াম ইউজাররা বিজ্ঞাপন দেখবেন না</li>
                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i> টাইমার শেষ হলে অটো রিডাইরেক্ট হবে</li>
                                <li><i class="fas fa-check text-success me-2"></i> পপআন্ডার বিজ্ঞাপন ৪-৫ সেকেন্ড পর বন্ধ হবে</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer text-center">
                <a href="index.php" class="btn btn-secondary me-2">
                    <i class="fas fa-home"></i> হোমে ফিরুন
                </a>
                <a href="test_download.php" class="btn btn-primary me-2">
                    <i class="fas fa-play"></i> টেস্ট করুন
                </a>
                <a href="setup_download_simple.php" class="btn btn-success">
                    <i class="fas fa-cog"></i> সেটআপ
                </a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const downloadTimer = parseInt(document.querySelector('input[name="download_timer"]').value);
            const premiumTimer = parseInt(document.querySelector('input[name="premium_timer"]').value);
            
            if (downloadTimer < 3 || downloadTimer > 10) {
                alert('সাধারণ ইউজার টাইমার ৩-১০ সেকেন্ডের মধ্যে হতে হবে');
                e.preventDefault();
                return;
            }
            
            if (premiumTimer < 1 || premiumTimer > 5) {
                alert('প্রিমিয়াম ইউজার টাইমার ১-৫ সেকেন্ডের মধ্যে হতে হবে');
                e.preventDefault();
                return;
            }
            
            if (premiumTimer >= downloadTimer) {
                alert('প্রিমিয়াম ইউজার টাইমার সাধারণ ইউজার টাইমারের চেয়ে কম হতে হবে');
                e.preventDefault();
                return;
            }
        });
    </script>
</body>
</html>
