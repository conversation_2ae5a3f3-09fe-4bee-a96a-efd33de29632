<?php
// Start output buffering to prevent 'headers already sent' errors
ob_start();

// Set session lifetime to 30 days (2592000 seconds)
ini_set('session.gc_maxlifetime', 2592000);
ini_set('session.cookie_lifetime', 2592000);
ini_set('session.gc_probability', 1);
ini_set('session.gc_divisor', 1000);

// Set session cookie parameters before starting session
session_set_cookie_params([
    'lifetime' => 2592000, // 30 days
    'path' => '/',
    'domain' => '',
    'secure' => false, // Set to true if using HTTPS
    'httponly' => true,
    'samesite' => 'Lax'
]);

// Start session at the very beginning
if(!isset($_SESSION)) {
    session_start();

    // Regenerate session ID periodically for security
    if (!isset($_SESSION['last_regeneration'])) {
        $_SESSION['last_regeneration'] = time();
    } elseif (time() - $_SESSION['last_regeneration'] > 3600) { // Regenerate every hour
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
    }
}

// Database configuration
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'tipsbdxy_4525');
define('DB_PASSWORD', '@mdsrabon13');
define('DB_NAME', 'tipsbdxy_4525');

// Attempt to connect to MySQL database
$conn = mysqli_connect(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_NAME);

// Check connection
if($conn === false){
    die("ERROR: Could not connect to database. " . mysqli_connect_error());
}

// Set character set
mysqli_set_charset($conn, "utf8mb4");

// Site configuration
define('SITE_NAME', 'CinePix');

// TMDB API configuration
if (!defined('TMDB_API_KEY')) define('TMDB_API_KEY', '3d36f64b789ec5484c76838f0ba11daf');
if (!defined('TMDB_LANGUAGE')) define('TMDB_LANGUAGE', 'en-US');

// JW Player configuration
define('JW_PLAYER_LICENSE', '64HPbvSQorQcd52B8XFuhMtEoitbvY/EXJmMBfKcXZQU2Rnn');

// Player configuration
define('DEFAULT_PLAYER', 'shaka'); // Options: 'default', 'plyr', 'shaka'

// Check if SITE_URL is already defined in the database settings
// If not, automatically detect it
if (!defined('SITE_URL')) {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $site_url = $protocol . '://' . $_SERVER['HTTP_HOST'];

    // Get the base directory (remove admin or includes from path)
    $script_path = dirname($_SERVER['SCRIPT_NAME']);
    $base_dir = '';

    // If we're in the admin directory, go up one level
    if (strpos($script_path, '/admin') !== false) {
        $base_dir = str_replace('/admin', '', $script_path);
    }
    // If we're in the includes directory, go up one level
    else if (strpos($script_path, '/includes') !== false) {
        $base_dir = str_replace('/includes', '', $script_path);
    }
    // Otherwise use the script path
    else {
        $base_dir = $script_path;
    }

    // If base_dir is just a slash, make it empty
    if ($base_dir === '/' || $base_dir === '\\') {
        $base_dir = '';
    }

    // Define site URL
    define('SITE_URL', $site_url . $base_dir);
}

// Additional site settings
if (!defined('SITE_EMAIL')) define('SITE_EMAIL', '<EMAIL>');
if (!defined('SITE_DESCRIPTION')) define('SITE_DESCRIPTION', 'Watch and download movies and TV shows');
if (!defined('SITE_KEYWORDS')) define('SITE_KEYWORDS', 'movies, tv shows, streaming, download, entertainment');

// Payment configuration
define('BKASH_APP_KEY', '5tunt4masn6pv2hnvte1sb5n3j');
define('BKASH_APP_SECRET', '1vggbqd4hqk9g96o9rrrp2jftvek578v7d2bnerim12a87dbrrka');
define('BKASH_USERNAME', 'sandboxTestUser');
define('BKASH_PASSWORD', 'hWD@8vtzw0');
define('BKASH_SANDBOX_URL', 'https://checkout.sandbox.bka.sh/v1.2.0-beta');

define('NAGAD_MERCHANT_ID', 'your_nagad_merchant_id');
define('NAGAD_MERCHANT_NUMBER', '01XXXXXXXXX');

define('ROCKET_MERCHANT_NUMBER', '01XXXXXXXXX');

// Session already started at the beginning of the file

// Function to check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// Function to check if user is admin
function isAdmin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'admin';
}

// Function to check if user can view content (all users can view content)
function canViewContent() {
    return true; // Allow all users to view content
}

// Function to check if user is premium
function isPremiumUser() {
    return isset($_SESSION['is_premium']) && $_SESSION['is_premium'] == 1;
}

// Function to check if user is premium
function isPremium() {
    global $conn;

    // If logged in, check from database (always check from database to ensure latest status)
    if (isLoggedIn()) {
        $user_id = $_SESSION['user_id'];

        // First check if user is marked as premium in users table
        $user_query = "SELECT is_premium FROM users WHERE id = $user_id";
        $user_result = mysqli_query($conn, $user_query);
        $user_data = mysqli_fetch_assoc($user_result);
        $is_premium_user = $user_data['is_premium'];

        // Then check for active subscriptions
        $subscription_query = "SELECT COUNT(*) as count FROM subscriptions
                             WHERE user_id = $user_id AND status = 'active' AND end_date > NOW()";
        $subscription_result = mysqli_query($conn, $subscription_query);
        $has_active_subscription = mysqli_fetch_assoc($subscription_result)['count'] > 0;

        // If user is marked as premium but has no active subscriptions, update user table
        if ($is_premium_user && !$has_active_subscription) {
            // Update user table to match subscription status
            $update_query = "UPDATE users SET is_premium = 0 WHERE id = $user_id";
            mysqli_query($conn, $update_query);
            $is_premium_user = 0;
        }

        // If user has active subscription but is not marked as premium, update user table
        if (!$is_premium_user && $has_active_subscription) {
            // Update user table to match subscription status
            $update_query = "UPDATE users SET is_premium = 1 WHERE id = $user_id";
            mysqli_query($conn, $update_query);
            $is_premium_user = 1;
        }

        // User is premium if both the user table says so and they have an active subscription
        // This ensures consistency between user table and subscriptions table
        $is_premium = $is_premium_user && $has_active_subscription;

        // Update session
        $_SESSION['is_premium'] = $is_premium;

        return $is_premium;
    }

    return false;
}

// Function to redirect
function redirect($url) {
    // Clean any existing output buffers
    while (ob_get_level()) {
        ob_end_clean();
    }

    // Set the header and exit
    header("Location: $url");
    exit();
}

// Function to sanitize input data
function sanitize($data) {
    global $conn;
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    $data = mysqli_real_escape_string($conn, $data);
    return $data;
}

// Include streaming helper functions
require_once 'streaming_helper.php';

// End with a PHP tag to prevent accidental whitespace
?>
