<?php
/**
 * Short Link Widget
 * Can be included in any page to generate short links
 */
?>

<div class="shortlink-widget">
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-link"></i> শর্ট লিংক তৈরি করুন</h5>
        </div>
        <div class="card-body">
            <form id="quickShortlinkForm">
                <div class="input-group mb-3">
                    <input type="url" class="form-control" id="quickUrl" placeholder="আপনার URL এখানে পেস্ট করুন..." required>
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-magic"></i> শর্ট করুন
                    </button>
                </div>
                
                <!-- Advanced Options (Collapsible) -->
                <div class="collapse" id="advancedOptions">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="quickTitle" class="form-label">টাইটেল (ঐচ্ছিক)</label>
                                <input type="text" class="form-control" id="quickTitle" placeholder="লিংকের টাইটেল">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="quickCustomCode" class="form-label">কাস্টম কোড (ঐচ্ছিক)</label>
                                <input type="text" class="form-control" id="quickCustomCode" placeholder="abc123" maxlength="10">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center">
                    <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-toggle="collapse" data-bs-target="#advancedOptions">
                        <i class="fas fa-cog"></i> আরও অপশন
                    </button>
                </div>
            </form>
            
            <!-- Result Section -->
            <div id="shortlinkResult" class="mt-3" style="display: none;">
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> শর্ট লিংক তৈরি হয়েছে!</h6>
                    <div class="input-group">
                        <input type="text" class="form-control" id="generatedShortlink" readonly>
                        <button class="btn btn-outline-success" onclick="copyShortlink()" title="কপি করুন">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button class="btn btn-outline-primary" onclick="openShortlink()" title="খুলুন">
                            <i class="fas fa-external-link-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Error Section -->
            <div id="shortlinkError" class="mt-3" style="display: none;">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span id="errorMessage"></span>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('quickShortlinkForm');
    
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            generateQuickShortlink();
        });
    }
});

function generateQuickShortlink() {
    const url = document.getElementById('quickUrl').value.trim();
    const title = document.getElementById('quickTitle').value.trim();
    const customCode = document.getElementById('quickCustomCode').value.trim();
    
    if (!url) {
        showShortlinkError('URL প্রয়োজন');
        return;
    }
    
    // Validate URL format
    try {
        new URL(url);
    } catch (e) {
        showShortlinkError('সঠিক URL ফরম্যাট দিন');
        return;
    }
    
    // Hide previous results
    document.getElementById('shortlinkResult').style.display = 'none';
    document.getElementById('shortlinkError').style.display = 'none';
    
    // Show loading state
    const submitBtn = document.querySelector('#quickShortlinkForm button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> তৈরি হচ্ছে...';
    submitBtn.disabled = true;
    
    const formData = {
        url: url,
        title: title || null,
        custom_code: customCode || null
    };
    
    fetch('api/shortlink.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showShortlinkResult(data.short_url);
            // Reset form
            document.getElementById('quickShortlinkForm').reset();
            // Collapse advanced options
            const advancedOptions = document.getElementById('advancedOptions');
            if (advancedOptions.classList.contains('show')) {
                bootstrap.Collapse.getInstance(advancedOptions).hide();
            }
        } else {
            showShortlinkError(data.error || 'লিংক তৈরি করতে সমস্যা হয়েছে');
        }
    })
    .catch(error => {
        console.error('Error creating shortlink:', error);
        showShortlinkError('নেটওয়ার্ক সমস্যা হয়েছে');
    })
    .finally(() => {
        // Restore button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

function showShortlinkResult(shortUrl) {
    document.getElementById('generatedShortlink').value = shortUrl;
    document.getElementById('shortlinkResult').style.display = 'block';
    document.getElementById('shortlinkError').style.display = 'none';
}

function showShortlinkError(message) {
    document.getElementById('errorMessage').textContent = message;
    document.getElementById('shortlinkError').style.display = 'block';
    document.getElementById('shortlinkResult').style.display = 'none';
}

function copyShortlink() {
    const shortlinkInput = document.getElementById('generatedShortlink');
    shortlinkInput.select();
    shortlinkInput.setSelectionRange(0, 99999); // For mobile devices
    
    navigator.clipboard.writeText(shortlinkInput.value).then(() => {
        // Show temporary success message
        const copyBtn = event.target.closest('button');
        const originalHTML = copyBtn.innerHTML;
        copyBtn.innerHTML = '<i class="fas fa-check"></i>';
        copyBtn.classList.remove('btn-outline-success');
        copyBtn.classList.add('btn-success');
        
        setTimeout(() => {
            copyBtn.innerHTML = originalHTML;
            copyBtn.classList.remove('btn-success');
            copyBtn.classList.add('btn-outline-success');
        }, 2000);
    }).catch(() => {
        // Fallback for older browsers
        document.execCommand('copy');
        alert('লিংক কপি হয়েছে!');
    });
}

function openShortlink() {
    const shortUrl = document.getElementById('generatedShortlink').value;
    window.open(shortUrl, '_blank');
}
</script>

<style>
.shortlink-widget .card {
    border: 1px solid #dee2e6;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.shortlink-widget .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.shortlink-widget .input-group .btn {
    border-left: 0;
}

.shortlink-widget .alert {
    margin-bottom: 0;
}

.shortlink-widget .collapse {
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .shortlink-widget .input-group {
        flex-direction: column;
    }
    
    .shortlink-widget .input-group .form-control {
        border-radius: 0.375rem;
        margin-bottom: 0.5rem;
    }
    
    .shortlink-widget .input-group .btn {
        border-radius: 0.375rem;
        border-left: 1px solid #ced4da;
    }
}
</style>
