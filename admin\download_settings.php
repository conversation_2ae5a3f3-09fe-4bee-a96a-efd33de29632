<?php
/**
 * Download Settings - Admin Panel
 * Configure download redirect timer and ads
 */

require_once '../includes/config.php';
require_once '../includes/auth.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit;
}

// Handle form submission
if ($_POST) {
    $download_timer = intval($_POST['download_timer'] ?? 4);
    $premium_timer = intval($_POST['premium_timer'] ?? 2);
    $show_ads = isset($_POST['show_ads']) ? 1 : 0;
    $popunder_ads = isset($_POST['popunder_ads']) ? 1 : 0;
    $ad_delay = intval($_POST['ad_delay'] ?? 2);
    
    // Validate timer values
    $download_timer = max(3, min(10, $download_timer));
    $premium_timer = max(1, min(5, $premium_timer));
    $ad_delay = max(1, min(10, $ad_delay));
    
    // Save settings to database or config file
    $settings = [
        'download_timer' => $download_timer,
        'premium_timer' => $premium_timer,
        'show_ads' => $show_ads,
        'popunder_ads' => $popunder_ads,
        'ad_delay' => $ad_delay
    ];
    
    // Create settings table if not exists
    $create_table = "CREATE TABLE IF NOT EXISTS download_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(50) NOT NULL UNIQUE,
        setting_value TEXT NOT NULL,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    mysqli_query($conn, $create_table);
    
    // Save each setting
    foreach ($settings as $key => $value) {
        $stmt = $conn->prepare("INSERT INTO download_settings (setting_key, setting_value) VALUES (?, ?) 
                               ON DUPLICATE KEY UPDATE setting_value = ?");
        $stmt->bind_param("sss", $key, $value, $value);
        $stmt->execute();
    }
    
    $success_message = "সেটিংস সফলভাবে সংরক্ষিত হয়েছে!";
}

// Get current settings
function getSetting($conn, $key, $default = '') {
    $stmt = $conn->prepare("SELECT setting_value FROM download_settings WHERE setting_key = ?");
    $stmt->bind_param("s", $key);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        return $result->fetch_assoc()['setting_value'];
    }
    
    return $default;
}

$current_settings = [
    'download_timer' => getSetting($conn, 'download_timer', 4),
    'premium_timer' => getSetting($conn, 'premium_timer', 2),
    'show_ads' => getSetting($conn, 'show_ads', 1),
    'popunder_ads' => getSetting($conn, 'popunder_ads', 1),
    'ad_delay' => getSetting($conn, 'ad_delay', 2)
];

$page_title = "ডাউনলোড সেটিংস";
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - CinePix Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
        }
        
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        
        .settings-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 20px;
        }
        
        .setting-group {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .setting-group:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .setting-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        
        .setting-description {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }
        
        .timer-input {
            max-width: 100px;
        }
        
        .preview-card {
            background: #f8f9fa;
            border: 1px dashed #dee2e6;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        
        .btn-save {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
        }
        
        .btn-save:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
    </style>
</head>
<body>
    <div class="admin-header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <h1><i class="fas fa-cog"></i> <?php echo $page_title; ?></h1>
                <a href="index.php" class="btn btn-light">
                    <i class="fas fa-arrow-left"></i> অ্যাডমিন প্যানেল
                </a>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <form method="POST">
            <div class="settings-card">
                <h3><i class="fas fa-clock"></i> টাইমার সেটিংস</h3>
                <p class="text-muted">ডাউনলোড রিডাইরেক্ট পেজের টাইমার কনফিগার করুন</p>

                <div class="setting-group">
                    <div class="setting-title">সাধারণ ইউজার টাইমার</div>
                    <div class="setting-description">
                        সাধারণ ইউজারদের জন্য ডাউনলোড টাইমার (৩-১০ সেকেন্ড)
                    </div>
                    <div class="input-group timer-input">
                        <input type="number" class="form-control" name="download_timer" 
                               value="<?php echo $current_settings['download_timer']; ?>" 
                               min="3" max="10" required>
                        <span class="input-group-text">সেকেন্ড</span>
                    </div>
                </div>

                <div class="setting-group">
                    <div class="setting-title">প্রিমিয়াম ইউজার টাইমার</div>
                    <div class="setting-description">
                        প্রিমিয়াম ইউজারদের জন্য কম টাইমার (১-৫ সেকেন্ড)
                    </div>
                    <div class="input-group timer-input">
                        <input type="number" class="form-control" name="premium_timer" 
                               value="<?php echo $current_settings['premium_timer']; ?>" 
                               min="1" max="5" required>
                        <span class="input-group-text">সেকেন্ড</span>
                    </div>
                </div>
            </div>

            <div class="settings-card">
                <h3><i class="fas fa-ad"></i> বিজ্ঞাপন সেটিংস</h3>
                <p class="text-muted">ডাউনলোড পেজে বিজ্ঞাপন কনফিগার করুন</p>

                <div class="setting-group">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" name="show_ads" 
                               <?php echo $current_settings['show_ads'] ? 'checked' : ''; ?>>
                        <label class="form-check-label setting-title">
                            বিজ্ঞাপন দেখান
                        </label>
                    </div>
                    <div class="setting-description">
                        ডাউনলোড পেজে বিজ্ঞাপন দেখানো হবে (শুধুমাত্র সাধারণ ইউজারদের জন্য)
                    </div>
                </div>

                <div class="setting-group">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" name="popunder_ads" 
                               <?php echo $current_settings['popunder_ads'] ? 'checked' : ''; ?>>
                        <label class="form-check-label setting-title">
                            পপআন্ডার বিজ্ঞাপন
                        </label>
                    </div>
                    <div class="setting-description">
                        পপআন্ডার বিজ্ঞাপন সক্রিয় করুন (নতুন ট্যাবে খুলবে)
                    </div>
                </div>

                <div class="setting-group">
                    <div class="setting-title">বিজ্ঞাপন বিলম্ব</div>
                    <div class="setting-description">
                        পপআন্ডার বিজ্ঞাপন কত সেকেন্ড পর দেখাবে
                    </div>
                    <div class="input-group timer-input">
                        <input type="number" class="form-control" name="ad_delay" 
                               value="<?php echo $current_settings['ad_delay']; ?>" 
                               min="1" max="10" required>
                        <span class="input-group-text">সেকেন্ড</span>
                    </div>
                </div>
            </div>

            <div class="settings-card">
                <h3><i class="fas fa-eye"></i> প্রিভিউ</h3>
                <div class="preview-card">
                    <div class="mb-3">
                        <i class="fas fa-download fa-2x text-primary"></i>
                    </div>
                    <h5>ডাউনলোড প্রস্তুত হচ্ছে...</h5>
                    <div class="badge bg-primary me-2">1080p</div>
                    <div class="badge bg-secondary">Server 1</div>
                    <div class="mt-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                    <p class="mt-2 text-muted">
                        অনুগ্রহ করে <strong><?php echo $current_settings['download_timer']; ?></strong> সেকেন্ড অপেক্ষা করুন...
                    </p>
                </div>
            </div>

            <div class="text-center">
                <button type="submit" class="btn btn-save">
                    <i class="fas fa-save"></i> সেটিংস সংরক্ষণ করুন
                </button>
            </div>
        </form>

        <div class="mt-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle"></i> গুরুত্বপূর্ণ তথ্য</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> প্রিমিয়াম ইউজাররা কম টাইমার পাবেন</li>
                        <li><i class="fas fa-check text-success"></i> প্রিমিয়াম ইউজাররা বিজ্ঞাপন দেখবেন না</li>
                        <li><i class="fas fa-check text-success"></i> টাইমার শেষ হলে অটো রিডাইরেক্ট হবে</li>
                        <li><i class="fas fa-check text-success"></i> পপআন্ডার বিজ্ঞাপন ৪-৫ সেকেন্ড পর বন্ধ হবে</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Update preview when timer changes
        document.querySelector('input[name="download_timer"]').addEventListener('input', function() {
            const previewText = document.querySelector('.preview-card p strong');
            if (previewText) {
                previewText.textContent = this.value;
            }
        });
        
        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const downloadTimer = parseInt(document.querySelector('input[name="download_timer"]').value);
            const premiumTimer = parseInt(document.querySelector('input[name="premium_timer"]').value);
            
            if (downloadTimer < 3 || downloadTimer > 10) {
                alert('সাধারণ ইউজার টাইমার ৩-১০ সেকেন্ডের মধ্যে হতে হবে');
                e.preventDefault();
                return;
            }
            
            if (premiumTimer < 1 || premiumTimer > 5) {
                alert('প্রিমিয়াম ইউজার টাইমার ১-৫ সেকেন্ডের মধ্যে হতে হবে');
                e.preventDefault();
                return;
            }
            
            if (premiumTimer >= downloadTimer) {
                alert('প্রিমিয়াম ইউজার টাইমার সাধারণ ইউজার টাইমারের চেয়ে কম হতে হবে');
                e.preventDefault();
                return;
            }
        });
    </script>
</body>
</html>
