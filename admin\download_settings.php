<?php
/**
 * Download Settings - Admin Panel
 * Configure download redirect timer and ads
 */

// Include configuration file
require_once '../includes/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(SITE_URL);
}

// Get current page for sidebar
$current_page = 'download_settings.php';

// Handle form submission
if ($_POST) {
    $download_timer = intval($_POST['download_timer'] ?? 4);
    $premium_timer = intval($_POST['premium_timer'] ?? 2);
    $show_ads = isset($_POST['show_ads']) ? 1 : 0;
    $popunder_ads = isset($_POST['popunder_ads']) ? 1 : 0;
    $ad_delay = intval($_POST['ad_delay'] ?? 2);
    
    // Validate timer values
    $download_timer = max(3, min(10, $download_timer));
    $premium_timer = max(1, min(5, $premium_timer));
    $ad_delay = max(1, min(10, $ad_delay));
    
    // Save settings to database or config file
    $settings = [
        'download_timer' => $download_timer,
        'premium_timer' => $premium_timer,
        'show_ads' => $show_ads,
        'popunder_ads' => $popunder_ads,
        'ad_delay' => $ad_delay
    ];
    
    // Create settings table if not exists
    $create_table = "CREATE TABLE IF NOT EXISTS download_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(50) NOT NULL UNIQUE,
        setting_value TEXT NOT NULL,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    mysqli_query($conn, $create_table);
    
    // Save each setting
    foreach ($settings as $key => $value) {
        $key_escaped = mysqli_real_escape_string($conn, $key);
        $value_escaped = mysqli_real_escape_string($conn, $value);

        $query = "INSERT INTO download_settings (setting_key, setting_value) VALUES ('$key_escaped', '$value_escaped')
                  ON DUPLICATE KEY UPDATE setting_value = '$value_escaped'";
        $conn->query($query);
    }
    
    $success_message = "সেটিংস সফলভাবে সংরক্ষিত হয়েছে!";
}

// Get current settings
function getSetting($conn, $key, $default = '') {
    // Check if table exists first
    $table_check = $conn->query("SHOW TABLES LIKE 'download_settings'");
    if ($table_check->num_rows == 0) {
        return $default;
    }

    // Use simple query instead of prepared statement for compatibility
    $key_escaped = mysqli_real_escape_string($conn, $key);
    $query = "SELECT setting_value FROM download_settings WHERE setting_key = '$key_escaped'";
    $result = $conn->query($query);

    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        return $row['setting_value'];
    }

    return $default;
}

$current_settings = [
    'download_timer' => getSetting($conn, 'download_timer', 4),
    'premium_timer' => getSetting($conn, 'premium_timer', 2),
    'show_ads' => getSetting($conn, 'show_ads', 1),
    'popunder_ads' => getSetting($conn, 'popunder_ads', 1),
    'ad_delay' => getSetting($conn, 'ad_delay', 2)
];

$page_title = "ডাউনলোড সেটিংস";

// Include header
include 'includes/header.php';

// Include sidebar
include 'includes/sidebar.php';
?>


<!-- Content -->
<div class="content">
    <!-- Topbar -->
    <div class="container-fluid mb-4">
        <div class="topbar">
            <div class="topbar-title d-flex align-items-center">
                <button class="topbar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1><?php echo $page_title; ?></h1>
            </div>
            <div class="topbar-actions">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="index.php">ড্যাশবোর্ড</a></li>
                        <li class="breadcrumb-item active">ডাউনলোড সেটিংস</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="container-fluid">

        <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <form method="POST">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-clock"></i> টাইমার সেটিংস
                    </h6>
                </div>
                <div class="card-body">
                    <p class="text-muted">ডাউনলোড রিডাইরেক্ট পেজের টাইমার কনফিগার করুন</p>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">সাধারণ ইউজার টাইমার</label>
                            <p class="text-muted small">সাধারণ ইউজারদের জন্য ডাউনলোড টাইমার (৩-১০ সেকেন্ড)</p>
                            <div class="input-group" style="max-width: 200px;">
                                <input type="number" class="form-control" name="download_timer"
                                       value="<?php echo $current_settings['download_timer']; ?>"
                                       min="3" max="10" required>
                                <span class="input-group-text">সেকেন্ড</span>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">প্রিমিয়াম ইউজার টাইমার</label>
                            <p class="text-muted small">প্রিমিয়াম ইউজারদের জন্য কম টাইমার (১-৫ সেকেন্ড)</p>
                            <div class="input-group" style="max-width: 200px;">
                                <input type="number" class="form-control" name="premium_timer"
                                       value="<?php echo $current_settings['premium_timer']; ?>"
                                       min="1" max="5" required>
                                <span class="input-group-text">সেকেন্ড</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-ad"></i> বিজ্ঞাপন সেটিংস
                    </h6>
                </div>
                <div class="card-body">
                    <p class="text-muted">ডাউনলোড পেজে বিজ্ঞাপন কনফিগার করুন</p>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="show_ads"
                                       <?php echo $current_settings['show_ads'] ? 'checked' : ''; ?>>
                                <label class="form-check-label fw-bold">
                                    বিজ্ঞাপন দেখান
                                </label>
                            </div>
                            <p class="text-muted small mt-2">
                                ডাউনলোড পেজে বিজ্ঞাপন দেখানো হবে (শুধুমাত্র সাধারণ ইউজারদের জন্য)
                            </p>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="popunder_ads"
                                       <?php echo $current_settings['popunder_ads'] ? 'checked' : ''; ?>>
                                <label class="form-check-label fw-bold">
                                    পপআন্ডার বিজ্ঞাপন
                                </label>
                            </div>
                            <p class="text-muted small mt-2">
                                পপআন্ডার বিজ্ঞাপন সক্রিয় করুন (নতুন ট্যাবে খুলবে)
                            </p>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">বিজ্ঞাপন বিলম্ব</label>
                            <p class="text-muted small">পপআন্ডার বিজ্ঞাপন কত সেকেন্ড পর দেখাবে</p>
                            <div class="input-group" style="max-width: 200px;">
                                <input type="number" class="form-control" name="ad_delay"
                                       value="<?php echo $current_settings['ad_delay']; ?>"
                                       min="1" max="10" required>
                                <span class="input-group-text">সেকেন্ড</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-eye"></i> প্রিভিউ
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center p-4 bg-light rounded">
                        <div class="mb-3">
                            <i class="fas fa-download fa-2x text-primary"></i>
                        </div>
                        <h5>ডাউনলোড প্রস্তুত হচ্ছে...</h5>
                        <div class="badge bg-primary me-2">1080p</div>
                        <div class="badge bg-secondary">Server 1</div>
                        <div class="mt-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        <p class="mt-2 text-muted">
                            অনুগ্রহ করে <strong><?php echo $current_settings['download_timer']; ?></strong> সেকেন্ড অপেক্ষা করুন...
                        </p>
                    </div>
                </div>
            </div>

            <div class="text-center">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-save"></i> সেটিংস সংরক্ষণ করুন
                </button>
            </div>
        </form>

        <div class="mt-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i> গুরুত্বপূর্ণ তথ্য
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i> প্রিমিয়াম ইউজাররা কম টাইমার পাবেন</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i> প্রিমিয়াম ইউজাররা বিজ্ঞাপন দেখবেন না</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i> টাইমার শেষ হলে অটো রিডাইরেক্ট হবে</li>
                        <li><i class="fas fa-check text-success me-2"></i> পপআন্ডার বিজ্ঞাপন ৪-৫ সেকেন্ড পর বন্ধ হবে</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update preview when timer changes
    const downloadTimerInput = document.querySelector('input[name="download_timer"]');
    if (downloadTimerInput) {
        downloadTimerInput.addEventListener('input', function() {
            const previewText = document.querySelector('.card-body p strong');
            if (previewText) {
                previewText.textContent = this.value;
            }
        });
    }

    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const downloadTimer = parseInt(document.querySelector('input[name="download_timer"]').value);
            const premiumTimer = parseInt(document.querySelector('input[name="premium_timer"]').value);

            if (downloadTimer < 3 || downloadTimer > 10) {
                alert('সাধারণ ইউজার টাইমার ৩-১০ সেকেন্ডের মধ্যে হতে হবে');
                e.preventDefault();
                return;
            }

            if (premiumTimer < 1 || premiumTimer > 5) {
                alert('প্রিমিয়াম ইউজার টাইমার ১-৫ সেকেন্ডের মধ্যে হতে হবে');
                e.preventDefault();
                return;
            }

            if (premiumTimer >= downloadTimer) {
                alert('প্রিমিয়াম ইউজার টাইমার সাধারণ ইউজার টাইমারের চেয়ে কম হতে হবে');
                e.preventDefault();
                return;
            }
        });
    }
});
</script>
