<?php
require_once 'includes/config.php';
require_once 'includes/streaming_helper.php';
require_once 'includes/ad_placeholder.php';

// Load SEO files with error handling
if (file_exists('includes/seo_helper.php')) {
    require_once 'includes/seo_helper.php';
}
if (file_exists('includes/seo_content.php')) {
    require_once 'includes/seo_content.php';
}

// Check if id is provided first
if (!isset($_GET['id'])) {
    header('Location: ' . SITE_URL);
    exit;
}

$id = (int)$_GET['id'];
$movie_id = $id; // For compatibility with other functions

// Get movie details for SEO
$query = "SELECT m.*, c.name as category_name, m.premium_only FROM movies m
          LEFT JOIN categories c ON m.category_id = c.id
          WHERE m.id = $id";
$result = mysqli_query($conn, $query);

// Check if movie exists
if (mysqli_num_rows($result) == 0) {
    header('Location: ' . SITE_URL);
    exit;
}

$movie = mysqli_fetch_assoc($result);

// SEO Meta Tags Configuration
$page_title = $movie['title'] . " (" . $movie['release_year'] . ") - Download Full Movie | " . SITE_NAME;
$page_description = "Download " . $movie['title'] . " (" . $movie['release_year'] . ") full movie in HD quality. " . substr($movie['description'], 0, 120) . "...";
$page_keywords = function_exists('getMovieKeywords') ? getMovieKeywords($movie) : $movie['title'] . ", download, full movie";
$page_image = SITE_URL . "/uploads/" . $movie['poster'];
$canonical_url = function_exists('getMovieSeoUrl') ? getMovieSeoUrl($movie['id'], $movie['title'], $movie['release_year']) : SITE_URL . "/movie_details.php?id=" . $movie['id'];

// Include header after setting SEO variables
require_once 'includes/header.php';

// Add custom CSS for movie details page
echo '<link rel="stylesheet" href="' . SITE_URL . '/css/movie-details.css">';

// Add custom CSS for movie cards
echo '<style>
    /* Movie Card Link Style */
    .movie-card-link {
        display: block;
        text-decoration: none;
        color: white;
        height: 100%;
    }

    .movie-card-link:hover {
        text-decoration: none;
        color: white;
    }

    /* Similar Movies Carousel Styles */
    .similar-movies-section {
        background-color: #0a0a0a;
        position: relative;
        overflow: hidden;
    }

    .section-header {
        margin-bottom: 20px;
    }

    .section-title {
        color: #fff;
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 0;
        position: relative;
        padding-left: 15px;
        border-left: 4px solid #ff7b00;
    }

    .carousel-controls {
        display: flex;
        gap: 10px;
    }

    .carousel-control-prev,
    .carousel-control-next {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.1);
        border: none;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }

    .carousel-control-prev:hover,
    .carousel-control-next:hover {
        background-color: #ff7b00;
    }

    .similar-movies-carousel {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 15px;
        overflow-x: auto;
        padding-bottom: 15px;
        scroll-behavior: smooth;
        scrollbar-width: thin;
        scrollbar-color: #ff7b00 #1a1a1a;
    }

    .similar-movies-carousel::-webkit-scrollbar {
        height: 6px;
    }

    .similar-movies-carousel::-webkit-scrollbar-track {
        background: #1a1a1a;
        border-radius: 10px;
    }

    .similar-movies-carousel::-webkit-scrollbar-thumb {
        background-color: #ff7b00;
        border-radius: 10px;
    }

    .similar-movie-item {
        min-width: 160px;
    }

    .similar-movie-link {
        text-decoration: none;
        color: #fff;
        display: block;
    }

    .similar-movie-card {
        background-color: #1a1a1a;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .similar-movie-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    }

    .similar-movie-poster {
        position: relative;
        aspect-ratio: 2/3;
        overflow: hidden;
    }

    .similar-movie-poster img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .similar-movie-card:hover .similar-movie-poster img {
        transform: scale(1.05);
    }

    .premium-tag {
        position: absolute;
        top: 8px;
        left: 8px;
        background-color: #ff7b00;
        color: white;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.7rem;
        z-index: 2;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    }

    .trailer-btn {
        position: absolute;
        top: 8px;
        right: 8px;
        z-index: 2;
    }

    .trailer-btn a {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.6);
        color: #fff;
        transition: all 0.3s ease;
    }

    .trailer-btn a:hover {
        background-color: #ff7b00;
    }

    .similar-movie-info {
        padding: 10px;
    }

    .similar-movie-title {
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: 5px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .similar-movie-meta {
        display: flex;
        justify-content: space-between;
        font-size: 0.8rem;
        color: #aaa;
    }

    .similar-movie-meta .rating {
        color: #ffc107;
    }

    /* Subtitle Button Styles */
    .subtitle-dropdown {
        position: relative;
        display: inline-block;
    }

    .subtitle-btn {
        background-color: #333;
        color: white;
        border: none;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-left: 5px;
    }

    .subtitle-btn:hover {
        background-color: #555;
    }

    .subtitle-dropdown-menu {
        min-width: 180px;
        background-color: #222;
        border: 1px solid #444;
    }

    .subtitle-dropdown-menu .dropdown-item {
        color: white;
        padding: 8px 15px;
        font-size: 0.9rem;
    }

    .subtitle-dropdown-menu .dropdown-item:hover {
        background-color: #333;
    }

    .modal-subtitle-dropdown {
        margin-top: 10px;
    }

    .modal-subtitle-dropdown .subtitle-btn {
        width: auto;
        height: auto;
        border-radius: 4px;
        padding: 5px 10px;
    }

    .download-link-container {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .download-link-container .subtitle-dropdown {
        display: flex;
        justify-content: center;
        margin-top: 5px;
    }

    /* Local Player Modal Styles */
    .player-options {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .player-option {
        display: flex;
        align-items: center;
        padding: 15px;
        border: 2px solid #333;
        border-radius: 8px;
        background-color: #1a1a1a;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .player-option:hover {
        border-color: #ff7b00;
        background-color: #2a2a2a;
        transform: translateY(-2px);
    }

    .player-icon {
        font-size: 2rem;
        margin-right: 15px;
        width: 50px;
        text-align: center;
    }

    .player-info h6 {
        margin: 0;
        color: #fff;
        font-weight: 600;
    }

    .player-info small {
        color: #aaa;
        font-size: 0.85rem;
    }

    .direct-link-section {
        border-top: 1px solid #333;
        padding-top: 15px;
    }

    .direct-link-section .form-label {
        color: #fff;
        font-weight: 500;
    }

    .direct-link-section .form-control {
        background-color: #2a2a2a;
        border-color: #444;
        color: #fff;
    }

    .direct-link-section .form-control:focus {
        background-color: #2a2a2a;
        border-color: #ff7b00;
        color: #fff;
        box-shadow: 0 0 0 0.2rem rgba(255, 123, 0, 0.25);
    }

    .watch-online-btn {
        background-color: #ff7b00;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 5px;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 5px;
        transition: all 0.3s ease;
        cursor: pointer;
        font-size: 0.9rem;
    }

    .watch-online-btn:hover {
        background-color: #e66a00;
        color: white;
        text-decoration: none;
        transform: translateY(-1px);
    }

    .play-link {
        background-color: #28a745;
        color: white;
        border: none;
        padding: 10px 15px;
        border-radius: 5px;
        display: flex;
        align-items: center;
        gap: 10px;
        transition: all 0.3s ease;
        cursor: pointer;
        text-decoration: none;
        margin-top: 10px;
    }

    .play-link:hover {
        background-color: #218838;
        color: white;
        text-decoration: none;
        transform: translateY(-1px);
    }

    /* All Player Links Modal Styles */
    .all-player-links {
        text-align: left;
    }

    .all-player-links h6 {
        color: #333;
        border-bottom: 2px solid #ff7b00;
        padding-bottom: 5px;
    }

    .player-link-item {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 5px;
        border-left: 3px solid #ff7b00;
    }

    .player-link-item strong {
        color: #333;
    }

    .player-link-item .form-control {
        font-size: 0.85rem;
        font-family: monospace;
    }

    /* SweetAlert Player Links Popup */
    .player-links-popup {
        background-color: #fff !important;
    }

    .player-links-popup .all-player-links {
        max-height: 400px;
        overflow-y: auto;
    }

    /* Device-specific player options */
    @media (max-width: 768px) {
        .desktop-only {
            display: none !important;
        }
    }

    @media (min-width: 769px) {
        .mobile-only {
            display: none !important;
        }
    }

    /* Player Help Popup */
    .player-help-popup {
        background-color: #fff !important;
    }

    .player-help-content {
        text-align: left;
    }

    .player-help-content h6 {
        color: #333;
        margin-top: 15px;
        margin-bottom: 10px;
        border-bottom: 1px solid #eee;
        padding-bottom: 5px;
    }

    .player-help-content h6:first-child {
        margin-top: 0;
        border-bottom: 2px solid #ff7b00;
        color: #ff7b00;
    }

    .help-section {
        margin-bottom: 15px;
    }

    .help-section ul {
        margin-bottom: 0;
        padding-left: 20px;
    }

    .help-section li {
        margin-bottom: 5px;
        color: #555;
    }

    .help-section strong {
        color: #333;
    }

    /* Responsive styles */
    @media (max-width: 1199.98px) {
        .similar-movies-carousel {
            grid-template-columns: repeat(5, 1fr);
        }
    }

    @media (max-width: 991.98px) {
        .similar-movies-carousel {
            grid-template-columns: repeat(4, 1fr);
        }
    }

    @media (max-width: 767.98px) {
        .similar-movies-carousel {
            grid-template-columns: repeat(3, 1fr);
        }

        .similar-movie-title {
            font-size: 0.85rem;
        }

        .similar-movie-meta {
            font-size: 0.75rem;
        }
    }

    @media (max-width: 575.98px) {
        .similar-movies-carousel {
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }

        .section-title {
            font-size: 1.2rem;
        }

        .similar-movie-item {
            min-width: 140px;
        }

        .similar-movie-info {
            padding: 8px;
        }

        .similar-movie-title {
            font-size: 0.8rem;
        }

        .similar-movie-meta {
            font-size: 0.7rem;
        }

        .premium-tag,
        .trailer-btn a {
            width: 20px;
            height: 20px;
            font-size: 0.6rem;
        }

        /* Mobile responsive for player options */
        .player-option {
            padding: 12px;
        }

        .player-icon {
            font-size: 1.5rem;
            margin-right: 10px;
            width: 40px;
        }

        .player-info h6 {
            font-size: 0.9rem;
        }

        .player-info small {
            font-size: 0.75rem;
        }

        .watch-online-btn {
            padding: 6px 12px;
            font-size: 0.8rem;
        }

        .play-link {
            padding: 8px 12px;
            font-size: 0.9rem;
        }
    }
</style>';

// Movie details already loaded from the top of the file

// Get all genres for the movie
$genres_query = "SELECT c.name FROM movie_genres mg
               JOIN categories c ON mg.genre_id = c.id
               WHERE mg.movie_id = $id
               ORDER BY c.name";
$genres_result = mysqli_query($conn, $genres_query);
$result = mysqli_query($conn, $query);

// Check if movie exists
if (mysqli_num_rows($result) == 0) {
    redirect(SITE_URL);
}

$movie = mysqli_fetch_assoc($result);

// Get reviews
$reviews_query = "SELECT r.*, u.username, u.profile_image FROM reviews r
                 JOIN users u ON r.user_id = u.id
                 WHERE r.content_type = 'movie' AND r.content_id = $id
                 ORDER BY r.created_at DESC";
$reviews_result = mysqli_query($conn, $reviews_query);

// Get download links
$download_query = "SELECT * FROM download_links
                  WHERE content_type = 'movie' AND content_id = $id
                  ORDER BY quality DESC, link_type ASC";
$download_result = mysqli_query($conn, $download_query);

// Get streaming links
$stream_query = "SELECT * FROM streaming_links
                WHERE content_type = 'movie' AND content_id = $id
                ORDER BY quality DESC, server_name ASC";
$stream_result = mysqli_query($conn, $stream_query);

// Get similar movies
$similar_query = "SELECT m.*, c.name as category_name FROM movies m
                 LEFT JOIN categories c ON m.category_id = c.id
                 WHERE m.category_id = {$movie['category_id']} AND m.id != $id
                 ORDER BY RAND()
                 LIMIT 4";
$similar_result = mysqli_query($conn, $similar_query);

// Prepare genres array for structured data
$genres_array = [];
if (isset($genres_result) && $genres_result && mysqli_num_rows($genres_result) > 0) {
    mysqli_data_seek($genres_result, 0); // Reset pointer
    while($genre = mysqli_fetch_assoc($genres_result)) {
        $genres_array[] = $genre['name'];
    }
}
// Fallback if no genres found
if (empty($genres_array)) {
    $genres_array = [$movie['category_name']];
}

// Generate SEO content (simplified)
$seo_content = ['faq' => []];
$internal_links = [];
$breadcrumb = [];
$breadcrumb_schema = [];
$faq_schema = [];

// Try to generate SEO content if functions exist
try {
    if (function_exists('generateMovieSeoContent')) {
        $seo_content = generateMovieSeoContent($movie);
    }
    if (function_exists('generateInternalLinks')) {
        $internal_links = generateInternalLinks('movie', $movie['category_id'], $movie['id']);
    }
    if (function_exists('getMovieBreadcrumb')) {
        $breadcrumb = getMovieBreadcrumb($movie);
    }
    if (function_exists('generateBreadcrumbSchema') && !empty($breadcrumb)) {
        $breadcrumb_schema = generateBreadcrumbSchema($breadcrumb);
    }
    if (function_exists('generateFAQJsonLD') && !empty($seo_content['faq'])) {
        $faq_schema = generateFAQJsonLD($seo_content['faq']);
    }
} catch (Exception $e) {
    // Ignore SEO generation errors
}
?>

<!-- JSON-LD Structured Data for Movie -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Movie",
  "name": "<?php echo addslashes($movie['title']); ?>",
  "alternateName": "<?php echo addslashes($movie['title']); ?> Full Movie",
  "description": "<?php echo addslashes(substr($movie['description'], 0, 200)); ?>",
  "image": "<?php echo SITE_URL; ?>/uploads/<?php echo $movie['poster']; ?>",
  "datePublished": "<?php echo $movie['release_year']; ?>-01-01",
  "genre": <?php echo json_encode($genres_array); ?>,
  "duration": "PT<?php echo !empty($movie['duration']) ? str_replace(' min', 'M', $movie['duration']) : '120M'; ?>",
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "<?php echo $movie['rating']; ?>",
    "bestRating": "10",
    "worstRating": "1",
    "ratingCount": "<?php echo rand(100, 1000); ?>"
  },
  "contentRating": "<?php echo !empty($movie['content_rating']) ? $movie['content_rating'] : 'Not Rated'; ?>",
  "inLanguage": "<?php echo !empty($movie['language']) ? $movie['language'] : 'Bengali'; ?>",
  "url": "<?php echo SITE_URL; ?>/movie_details.php?id=<?php echo $movie['id']; ?>",
  "potentialAction": {
    "@type": "WatchAction",
    "target": "<?php echo SITE_URL; ?>/movie_details.php?id=<?php echo $movie['id']; ?>"
  },
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "BDT",
    "availability": "https://schema.org/InStock"
  }
}
</script>

<!-- Movie Details Section -->
<section class="movie-details-section">
    <div class="movie-backdrop" style="background-image: url('<?php echo SITE_URL; ?>/uploads/<?php echo $movie['banner']; ?>')">
        <div class="backdrop-overlay"></div>
    </div>

    <div class="container">
        <div class="movie-details-container">
            <div class="row">
                <!-- Movie Poster -->
                <div class="col-md-3 col-lg-3 mb-4">
                    <div class="movie-poster-container">
                        <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $movie['poster']; ?>" alt="<?php echo $movie['title']; ?>" class="movie-poster">
                        <?php if($movie['premium_only']): ?>
                        <div class="premium-badge">
                            <i class="fas fa-crown"></i> PREMIUM
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Movie Info -->
                <div class="col-md-9 col-lg-9">
                    <div class="movie-info">
                        <h1 class="movie-title"><?php echo $movie['title']; ?></h1>

                        <div class="movie-meta">
                            <span class="movie-rating"><i class="fas fa-star"></i> <?php echo number_format($movie['rating'], 1); ?></span>
                            <span class="movie-year"><i class="fas fa-calendar-alt"></i> <?php echo $movie['release_year']; ?></span>
                            <span class="movie-duration"><i class="fas fa-clock"></i> <?php echo $movie['duration']; ?></span>
                            <span class="movie-category"><i class="fas fa-film"></i> <?php echo $movie['category_name']; ?></span>
                            <?php if(!empty($movie['language'])): ?>
                            <span class="movie-language"><i class="fas fa-language"></i> <?php echo $movie['language']; ?></span>
                            <?php endif; ?>
                        </div>

                        <?php if(!empty($genres_array)): ?>
                        <div class="movie-genres mt-2">
                            <i class="fas fa-tags me-2"></i>
                            <?php echo implode(', ', $genres_array); ?>
                        </div>
                        <?php endif; ?>

                        <div class="movie-description">
                            <h4>Overview</h4>
                            <p><?php echo $movie['description']; ?></p>
                            <div class="telegram-join mt-3">
                                <a href="https://t.me/buycinepix" target="_blank" class="btn btn-telegram">
                                    <i class="fab fa-telegram"></i> টেলিগ্রাম গ্রুপে জয়েন করুন
                                </a>
                            </div>
                        </div>

                        <div class="movie-actions mt-4">
                            <button type="button" class="btn btn-download" data-bs-toggle="modal" data-bs-target="#downloadModal">
                                <i class="fas fa-download"></i> Download
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Content Tabs Section -->
<section class="content-tabs-section">
    <div class="container">
        <!-- Custom Tabs -->
        <div class="custom-tabs">
            <div class="custom-tab-buttons">
                <button class="tab-button active" data-tab="download">
                    <i class="fas fa-download"></i> Download
                </button>
                <button class="tab-button" data-tab="reviews">
                    <i class="fas fa-comments"></i> Reviews
                </button>
            </div>

            <div class="custom-tab-content">


                <!-- Download Tab -->
                <div class="tab-content-pane active" id="download-tab">
                    <?php if (mysqli_num_rows($download_result) > 0): ?>
                    <div class="download-container">
                        <div class="download-header">
                            <h3>Download Movie</h3>
                        </div>

                        <div class="download-links">
                            <div class="row">
                                <?php while($download = mysqli_fetch_assoc($download_result)): ?>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <?php if($download['is_premium'] && !$is_premium): ?>
                                    <a href="<?php echo SITE_URL; ?>/premium.php" class="download-card premium-download">
                                        <div class="download-card-icon">
                                            <i class="fas fa-crown"></i>
                                        </div>
                                        <div class="download-card-info">
                                            <span class="download-quality"><?php echo $download['quality']; ?></span>
                                            <?php if(!empty($download['server_name'])): ?>
                                            <span class="download-server"><?php echo $download['server_name']; ?></span>
                                            <?php endif; ?>
                                            <?php if(!empty($download['file_size'])): ?>
                                            <span class="download-size"><?php echo $download['file_size']; ?></span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="download-card-premium">
                                            <span>Premium</span>
                                        </div>
                                    </a>
                                    <?php else: ?>
                                    <div class="download-link-container">
                                        <?php
                                        require_once 'includes/shortlink_helper.php';
                                        $redirect_url = generateDownloadRedirectUrl(
                                            $download['link_url'],
                                            $movie['title'],
                                            $download['quality'],
                                            $download['server_name'] ?? 'Server'
                                        );
                                        ?>
                                        <a href="<?php echo $redirect_url; ?>" class="download-card download-btn" data-download-url="<?php echo $download['link_url']; ?>" target="_blank">
                                            <div class="download-card-icon">
                                                <i class="fas fa-download"></i>
                                            </div>
                                            <div class="download-card-info">
                                                <span class="download-quality"><?php echo $download['quality']; ?></span>
                                                <?php if(!empty($download['server_name'])): ?>
                                                <span class="download-server"><?php echo $download['server_name']; ?></span>
                                                <?php endif; ?>
                                                <?php if(!empty($download['file_size'])): ?>
                                                <span class="download-size"><?php echo $download['file_size']; ?></span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="download-card-action">
                                                <i class="fas fa-arrow-down"></i>
                                            </div>
                                        </a>
                                        <?php
                                        // Generate play URL from download link
                                        $play_url = getPlayUrlFromDownload(
                                            $download['link_url'],
                                            $movie['title'],
                                            SITE_URL . '/uploads/' . $movie['poster'],
                                            $download['is_premium'],
                                            $download['quality'],
                                            $download['server_name'],
                                            $movie_id,
                                            0, // series_id
                                            0, // season
                                            0, // episode
                                            $download['id'],
                                            $download['subtitle_url_bn'],
                                            $download['subtitle_url_en']
                                        );
                                        ?>
                                        <button type="button" class="watch-online-btn" onclick="showLocalPlayerOptions('<?php echo addslashes($download['link_url']); ?>', '<?php echo addslashes($movie['title']); ?>', '<?php echo addslashes(SITE_URL . '/uploads/' . $movie['poster']); ?>', '<?php echo $download['quality']; ?>', '<?php echo addslashes($download['server_name']); ?>')">
                                            <i class="fas fa-play"></i> দেখুন
                                        </button>
                                        <?php if(!empty($download['subtitle_url_bn']) || !empty($download['subtitle_url_en'])): ?>
                                        <div class="subtitle-dropdown">
                                            <button class="subtitle-btn" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="fas fa-closed-captioning"></i>
                                            </button>
                                            <ul class="dropdown-menu subtitle-dropdown-menu">
                                                <?php if(!empty($download['subtitle_url_bn'])): ?>
                                                <li><a class="dropdown-item" href="<?php echo $download['subtitle_url_bn']; ?>" target="_blank" download>
                                                    <i class="fas fa-download me-1"></i> বাংলা সাবটাইটেল
                                                </a></li>
                                                <?php endif; ?>
                                                <?php if(!empty($download['subtitle_url_en'])): ?>
                                                <li><a class="dropdown-item" href="<?php echo $download['subtitle_url_en']; ?>" target="_blank" download>
                                                    <i class="fas fa-download me-1"></i> ইংরেজি সাবটাইটেল
                                                </a></li>
                                                <?php endif; ?>
                                            </ul>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <?php endwhile; ?>
                            </div>
                        </div>
                    </div>
                    <?php else: ?>
                    <div class="no-content-message">
                        <p>No download links available for this movie.</p>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Reviews Tab -->
                <div class="tab-content-pane" id="reviews-tab">
                    <?php if(isLoggedIn()): ?>
                    <!-- Review Form -->
                    <div class="review-form-container">
                        <h3>Write a Review</h3>
                        <form id="reviewForm">
                            <input type="hidden" name="content_type" value="movie">
                            <input type="hidden" name="content_id" value="<?php echo $id; ?>">
                            <div class="mb-3">
                                <label for="rating" class="form-label">Rating</label>
                                <select class="form-select" id="rating" name="rating" required>
                                    <option value="">Select rating</option>
                                    <?php for($i = 10; $i >= 1; $i--): ?>
                                    <option value="<?php echo $i; ?>"><?php echo $i; ?>/10</option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="comment" class="form-label">Your Review</label>
                                <textarea class="form-control" id="comment" name="comment" rows="4" required></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">Submit Review</button>
                        </form>
                    </div>
                    <?php endif; ?>

                    <!-- Reviews List -->
                    <div class="reviews-container">
                        <h3>User Reviews</h3>
                        <?php if(mysqli_num_rows($reviews_result) > 0): ?>
                        <?php while($review = mysqli_fetch_assoc($reviews_result)): ?>
                        <div class="review-card">
                            <div class="review-header">
                                <div class="review-user">
                                    <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $review['profile_image']; ?>" alt="<?php echo $review['username']; ?>" class="review-user-img">
                                    <span class="review-username"><?php echo $review['username']; ?></span>
                                </div>
                                <div class="review-rating">
                                    <div class="stars-container">
                                        <?php for($i = 1; $i <= 5; $i++): ?>
                                        <?php if($i <= round($review['rating'] / 2)): ?>
                                        <i class="fas fa-star"></i>
                                        <?php else: ?>
                                        <i class="far fa-star"></i>
                                        <?php endif; ?>
                                        <?php endfor; ?>
                                    </div>
                                    <span class="rating-number"><?php echo $review['rating']; ?>/10</span>
                                </div>
                            </div>
                            <div class="review-body">
                                <p class="review-text"><?php echo $review['comment']; ?></p>
                                <div class="review-date">
                                    <small><?php echo date('F j, Y', strtotime($review['created_at'])); ?></small>
                                </div>
                            </div>
                        </div>
                        <?php endwhile; ?>
                        <?php else: ?>
                        <div class="no-reviews-message">
                            <p>No reviews yet. Be the first to review this movie!</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Ad Section After Reviews -->
<div class="container my-4">
    <?php echo renderRealAd('banner_728x90'); ?>
</div>

<!-- Local Player Selection Modal -->
<div class="modal fade" id="localPlayerModal" tabindex="-1" aria-labelledby="localPlayerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="localPlayerModalLabel">
                    <i class="fas fa-play-circle text-warning"></i> লোকাল প্লেয়ারে খুলুন
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="player-options">
                    <div class="player-option" onclick="openInPlayer('vlc')">
                        <div class="player-icon">
                            <i class="fas fa-play-circle" style="color: #ff8800;"></i>
                        </div>
                        <div class="player-info">
                            <h6>VLC Media Player</h6>
                            <small>সবচেয়ে জনপ্রিয় ও নির্ভরযোগ্য প্লেয়ার</small>
                        </div>
                    </div>

                    <div class="player-option mobile-only" onclick="openInPlayer('mx')">
                        <div class="player-icon">
                            <i class="fab fa-android" style="color: #3DDC84;"></i>
                        </div>
                        <div class="player-info">
                            <h6>MX Player</h6>
                            <small>অ্যান্ড্রয়েড ডিভাইসের জন্য সেরা প্লেয়ার</small>
                        </div>
                    </div>

                    <div class="player-option" onclick="openInPlayer('kodi')">
                        <div class="player-icon">
                            <i class="fas fa-tv" style="color: #17a2b8;"></i>
                        </div>
                        <div class="player-info">
                            <h6>Kodi</h6>
                            <small>স্মার্ট টিভি ও হোম থিয়েটারের জন্য</small>
                        </div>
                    </div>

                    <div class="player-option desktop-only" onclick="openInPlayer('potplayer')">
                        <div class="player-icon">
                            <i class="fab fa-windows" style="color: #0078d4;"></i>
                        </div>
                        <div class="player-info">
                            <h6>PotPlayer</h6>
                            <small>উইন্ডোজের জন্য উন্নত প্লেয়ার</small>
                        </div>
                    </div>

                    <div class="player-option" onclick="openInPlayer('mpv')">
                        <div class="player-icon">
                            <i class="fas fa-film" style="color: #28a745;"></i>
                        </div>
                        <div class="player-info">
                            <h6>MPV Player</h6>
                            <small>দ্রুত ও লাইটওয়েট প্লেয়ার</small>
                        </div>
                    </div>

                    <div class="player-option mobile-only" onclick="openInPlayer('nplayer')">
                        <div class="player-icon">
                            <i class="fab fa-apple" style="color: #007AFF;"></i>
                        </div>
                        <div class="player-info">
                            <h6>nPlayer</h6>
                            <small>iOS ডিভাইসের জন্য প্রিমিয়াম প্লেয়ার</small>
                        </div>
                    </div>

                    <div class="player-option" onclick="openInPlayer('browser')">
                        <div class="player-icon">
                            <i class="fas fa-globe" style="color: #dc3545;"></i>
                        </div>
                        <div class="player-info">
                            <h6>ব্রাউজারে খুলুন</h6>
                            <small>ওয়েব প্লেয়ারে সরাসরি দেখুন</small>
                        </div>
                    </div>

                    <div class="player-option" onclick="showAllPlayerLinks()" style="border-color: #ffc107; background-color: #2a2a1a;">
                        <div class="player-icon">
                            <i class="fas fa-list" style="color: #ffc107;"></i>
                        </div>
                        <div class="player-info">
                            <h6>সব প্লেয়ার লিংক</h6>
                            <small>সব প্লেয়ারের লিংক একসাথে দেখুন</small>
                        </div>
                    </div>
                </div>

                <div class="direct-link-section mt-3">
                    <label for="directLink" class="form-label">অথবা ডাইরেক্ট লিংক কপি করুন:</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="directLink" readonly>
                        <button class="btn btn-outline-secondary" type="button" onclick="copyDirectLink()">
                            <i class="fas fa-copy"></i> কপি
                        </button>
                    </div>
                    <div class="mt-2 text-center">
                        <button class="btn btn-sm btn-info" type="button" onclick="showPlayerHelp()">
                            <i class="fas fa-question-circle"></i> সাহায্য
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Download Modal -->
<div class="modal fade" id="downloadModal" tabindex="-1" aria-labelledby="downloadModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="downloadModalLabel">Download "<?php echo $movie['title']; ?>"</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <?php if (mysqli_num_rows($download_result) > 0): ?>
                <div class="download-options">
                    <?php
                    // Reset the result pointer
                    mysqli_data_seek($download_result, 0);

                    // Group downloads by quality
                    $downloads_by_quality = [];
                    while($download = mysqli_fetch_assoc($download_result)) {
                        $downloads_by_quality[$download['quality']][] = $download;
                    }

                    // Display downloads grouped by quality
                    foreach($downloads_by_quality as $quality => $downloads):
                    ?>
                    <div class="download-quality-group">
                        <h4 class="download-quality-title"><?php echo $quality; ?></h4>
                        <div class="download-links-list">
                            <?php foreach($downloads as $download): ?>
                            <?php if($download['is_premium'] && !$is_premium): ?>
                            <a href="<?php echo SITE_URL; ?>/premium.php" class="download-link premium-link">
                                <div class="download-link-type">
                                    <i class="fas fa-crown"></i> Premium
                                </div>
                                <div class="download-link-info">
                                    <?php if(!empty($download['server_name'])): ?>
                                    <span><?php echo $download['server_name']; ?></span>
                                    <?php endif; ?>
                                    <?php if(!empty($download['file_size'])): ?>
                                    <small class="d-block mt-1"><?php echo $download['file_size']; ?></small>
                                    <?php endif; ?>
                                </div>
                                <div class="download-link-action">
                                    <span>Upgrade</span>
                                </div>
                            </a>
                            <?php else: ?>
                            <div class="download-link-container">
                                <?php
                                $redirect_url = generateDownloadRedirectUrl(
                                    $download['link_url'],
                                    $movie['title'],
                                    $download['quality'],
                                    $download['server_name'] ?? 'Server'
                                );
                                ?>
                                <a href="<?php echo $redirect_url; ?>" class="download-link download-btn" data-download-url="<?php echo $download['link_url']; ?>" target="_blank">
                                    <div class="download-link-type">
                                        <i class="fas fa-download"></i> Download
                                    </div>
                                    <div class="download-link-info">
                                        <span><?php echo $quality; ?></span>
                                        <?php if(!empty($download['server_name'])): ?>
                                        <small class="d-block mt-1"><?php echo $download['server_name']; ?></small>
                                        <?php endif; ?>
                                        <?php if(!empty($download['file_size'])): ?>
                                        <small class="d-block mt-1"><?php echo $download['file_size']; ?></small>
                                        <?php endif; ?>
                                    </div>
                                    <div class="download-link-action">
                                        <i class="fas fa-arrow-down"></i>
                                    </div>
                                </a>
                                <?php
                                // Generate play URL from download link
                                $play_url = getPlayUrlFromDownload(
                                    $download['link_url'],
                                    $movie['title'],
                                    SITE_URL . '/uploads/' . $movie['poster'],
                                    $download['is_premium'],
                                    $download['quality'],
                                    $download['server_name'],
                                    $movie_id,
                                    0, // series_id
                                    0, // season
                                    0, // episode
                                    $download['id'],
                                    $download['subtitle_url_bn'],
                                    $download['subtitle_url_en']
                                );
                                ?>
                                <button type="button" class="play-link" onclick="showLocalPlayerOptions('<?php echo addslashes($download['link_url']); ?>', '<?php echo addslashes($movie['title']); ?>', '<?php echo addslashes(SITE_URL . '/uploads/' . $movie['poster']); ?>', '<?php echo $download['quality']; ?>', '<?php echo addslashes($download['server_name']); ?>')">
                                    <div class="play-link-icon">
                                        <i class="fas fa-play"></i>
                                    </div>
                                    <div class="play-link-text">Watch Online</div>
                                </button>
                                <?php if(!empty($download['subtitle_url_bn']) || !empty($download['subtitle_url_en'])): ?>
                                <div class="subtitle-dropdown modal-subtitle-dropdown">
                                    <button class="subtitle-btn" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-closed-captioning"></i> সাবটাইটেল
                                    </button>
                                    <ul class="dropdown-menu subtitle-dropdown-menu">
                                        <?php if(!empty($download['subtitle_url_bn'])): ?>
                                        <li><a class="dropdown-item" href="<?php echo $download['subtitle_url_bn']; ?>" target="_blank" download>
                                            <i class="fas fa-download me-1"></i> বাংলা সাবটাইটেল
                                        </a></li>
                                        <?php endif; ?>
                                        <?php if(!empty($download['subtitle_url_en'])): ?>
                                        <li><a class="dropdown-item" href="<?php echo $download['subtitle_url_en']; ?>" target="_blank" download>
                                            <i class="fas fa-download me-1"></i> ইংরেজি সাবটাইটেল
                                        </a></li>
                                        <?php endif; ?>
                                    </ul>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="no-downloads-message">
                    <p>No download links available for this movie.</p>
                </div>
                <?php endif; ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Ad Section Before Similar Movies -->
<div class="container my-4">
    <?php echo renderRealAd('medium_468x60'); ?>
</div>

<!-- Similar Movies Carousel Section -->
<section class="similar-movies-section py-5">
    <div class="container">
        <div class="section-header d-flex justify-content-between align-items-center mb-4">
            <h2 class="section-title">আরও দেখতে পারেন</h2>
            <div class="carousel-controls">
                <button class="carousel-control-prev" type="button" id="similar-prev">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="carousel-control-next" type="button" id="similar-next">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>

        <div class="similar-movies-carousel">
            <?php mysqli_data_seek($similar_result, 0); // Reset result pointer ?>
            <?php while($similar = mysqli_fetch_assoc($similar_result)): ?>
            <div class="similar-movie-item">
                <a href="<?php echo SITE_URL; ?>/movie_details.php?id=<?php echo $similar['id']; ?>" class="similar-movie-link">
                    <div class="similar-movie-card">
                        <div class="similar-movie-poster">
                            <?php if($similar['premium_only']): ?>
                            <div class="premium-tag">
                                <i class="fas fa-crown"></i>
                            </div>
                            <?php endif; ?>
                            <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $similar['poster']; ?>" alt="<?php echo $similar['title']; ?>" loading="lazy">
                            <?php if($similar['trailer_url']): ?>
                            <div class="trailer-btn">
                                <a href="#" class="play-trailer" data-trailer="<?php echo $similar['trailer_url']; ?>" onclick="event.preventDefault(); playTrailer('<?php echo $similar['trailer_url']; ?>');">
                                    <i class="fas fa-play"></i>
                                </a>
                            </div>
                            <?php endif; ?>
                        </div>
                        <div class="similar-movie-info">
                            <h3 class="similar-movie-title"><?php echo $similar['title']; ?></h3>
                            <div class="similar-movie-meta">
                                <span class="year"><?php echo $similar['release_year']; ?></span>
                                <span class="rating"><i class="fas fa-star"></i> <?php echo number_format($similar['rating'], 1); ?></span>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
            <?php endwhile; ?>
        </div>
    </div>
</section>

<?php require_once 'includes/footer.php'; ?>

<script>
    // Global variables for local player modal
    let currentVideoUrl = '';
    let currentVideoTitle = '';
    let currentVideoPoster = '';
    let currentVideoQuality = '';
    let currentVideoServer = '';

    // Show local player options modal
    function showLocalPlayerOptions(url, title, poster, quality, server) {
        currentVideoUrl = url;
        currentVideoTitle = title;
        currentVideoPoster = poster;
        currentVideoQuality = quality;
        currentVideoServer = server;

        // Set the direct link in the input field
        document.getElementById('directLink').value = url;

        // Show/hide player options based on device
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        const isAndroid = /Android/.test(navigator.userAgent);

        // Show appropriate player options
        document.querySelectorAll('.player-option').forEach(option => {
            option.style.display = 'flex'; // Show all by default
        });

        // Hide desktop-only options on mobile
        if (isMobile) {
            document.querySelectorAll('.desktop-only').forEach(option => {
                option.style.display = 'none';
            });
        }

        // Hide mobile-only options on desktop
        if (!isMobile) {
            document.querySelectorAll('.mobile-only').forEach(option => {
                option.style.display = 'none';
            });
        }

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('localPlayerModal'));
        modal.show();
    }

    // Open video in specific player
    function openInPlayer(playerType) {
        let playerUrl = '';

        switch(playerType) {
            case 'vlc':
                playerUrl = 'vlc://' + currentVideoUrl;
                break;
            case 'mx':
                // Check if it's mobile device
                const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
                if (isMobile) {
                    // MX Player intent for Android
                    playerUrl = 'intent:' + currentVideoUrl + '#Intent;package=com.mxtech.videoplayer.ad;S.title=' + encodeURIComponent(currentVideoTitle) + ';end';
                } else {
                    // For desktop, show instructions
                    showPlayerInstructions('mx');
                    return;
                }
                break;
            case 'kodi':
                playerUrl = 'kodi://play/' + encodeURIComponent(currentVideoUrl);
                break;
            case 'potplayer':
                playerUrl = 'potplayer://' + currentVideoUrl;
                break;
            case 'mpv':
                playerUrl = 'mpv://' + currentVideoUrl;
                break;
            case 'nplayer':
                // nPlayer for iOS
                playerUrl = 'nplayer-' + currentVideoUrl;
                break;
            case 'browser':
                // Generate play URL using the existing function logic
                let playerFile = 'play.php';

                // Check if URL is from Cloudflare Workers or has specific extensions
                if (currentVideoUrl.includes('workers.dev') ||
                    currentVideoUrl.toLowerCase().includes('.mkv') ||
                    currentVideoUrl.toLowerCase().includes('.m3u8') ||
                    currentVideoUrl.toLowerCase().includes('.mpd')) {
                    playerFile = 'direct_player.php';
                }

                const playUrl = '<?php echo SITE_URL; ?>/' + playerFile + '?url=' + encodeURIComponent(currentVideoUrl) +
                               '&title=' + encodeURIComponent(currentVideoTitle) +
                               '&poster=' + encodeURIComponent(currentVideoPoster) +
                               '&quality=' + encodeURIComponent(currentVideoQuality) +
                               '&server=' + encodeURIComponent(currentVideoServer);
                window.open(playUrl, '_blank');
                return;
        }

        // Try to open the player URL
        try {
            window.location.href = playerUrl;
        } catch (error) {
            // If direct opening fails, show a message with instructions
            showPlayerInstructions(playerType);
        }

        // Close the modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('localPlayerModal'));
        modal.hide();
    }

    // Show instructions for manual player opening
    function showPlayerInstructions(playerType) {
        let instructions = '';

        switch(playerType) {
            case 'vlc':
                instructions = 'VLC Player খুলুন এবং Media > Open Network Stream এ গিয়ে লিংকটি পেস্ট করুন।';
                break;
            case 'mx':
                instructions = 'MX Player শুধুমাত্র অ্যান্ড্রয়েড ডিভাইসে কাজ করে। অ্যান্ড্রয়েড ডিভাইস থেকে আবার চেষ্টা করুন অথবা অন্য প্লেয়ার ব্যবহার করুন।';
                break;
            case 'kodi':
                instructions = 'Kodi খুলুন এবং Videos > Files > Add videos এ গিয়ে লিংকটি যোগ করুন।';
                break;
            case 'potplayer':
                instructions = 'PotPlayer খুলুন এবং File > Open URL এ গিয়ে লিংকটি পেস্ট করুন।';
                break;
            case 'mpv':
                instructions = 'MPV Player খুলুন এবং File > Open URL এ গিয়ে লিংকটি পেস্ট করুন।';
                break;
            case 'nplayer':
                instructions = 'nPlayer খুলুন এবং Network Stream অপশনে গিয়ে লিংকটি পেস্ট করুন।';
                break;
        }

        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'প্লেয়ার খোলার নির্দেশনা',
                text: instructions,
                icon: 'info',
                confirmButtonText: 'বুঝেছি'
            });
        } else {
            alert(instructions);
        }
    }

    // Show all player links
    function showAllPlayerLinks() {
        const playerLinks = {
            'VLC Media Player': 'vlc://' + currentVideoUrl,
            'MX Player (Android)': 'intent:' + currentVideoUrl + '#Intent;package=com.mxtech.videoplayer.ad;S.title=' + encodeURIComponent(currentVideoTitle) + ';end',
            'Kodi': 'kodi://play/' + encodeURIComponent(currentVideoUrl),
            'PotPlayer': 'potplayer://' + currentVideoUrl,
            'MPV Player': 'mpv://' + currentVideoUrl,
            'nPlayer (iOS)': 'nplayer-' + currentVideoUrl
        };

        let linksHtml = '<div class="all-player-links">';
        linksHtml += '<h6 class="mb-3">সব প্লেয়ার লিংক:</h6>';

        for (const [playerName, playerUrl] of Object.entries(playerLinks)) {
            linksHtml += '<div class="player-link-item mb-2">';
            linksHtml += '<strong>' + playerName + ':</strong><br>';
            linksHtml += '<div class="input-group input-group-sm mt-1">';
            linksHtml += '<input type="text" class="form-control" value="' + playerUrl + '" readonly>';
            linksHtml += '<button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard(\'' + playerUrl.replace(/'/g, "\\'") + '\')">';
            linksHtml += '<i class="fas fa-copy"></i>';
            linksHtml += '</button>';
            linksHtml += '</div>';
            linksHtml += '</div>';
        }

        linksHtml += '</div>';

        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'সব প্লেয়ার লিংক',
                html: linksHtml,
                width: '600px',
                showConfirmButton: true,
                confirmButtonText: 'বন্ধ করুন',
                customClass: {
                    popup: 'player-links-popup'
                }
            });
        } else {
            // Fallback - show in a new modal or alert
            alert('এই ফিচারটি ব্যবহার করতে SweetAlert2 প্রয়োজন।');
        }
    }

    // Show player help
    function showPlayerHelp() {
        const helpContent = `
            <div class="player-help-content">
                <h6>লোকাল প্লেয়ার ব্যবহারের নির্দেশনা:</h6>
                <div class="help-section">
                    <h6><i class="fas fa-mobile-alt"></i> মোবাইল ডিভাইসে:</h6>
                    <ul>
                        <li><strong>MX Player (Android):</strong> সরাসরি ক্লিক করলে MX Player এ খুলবে</li>
                        <li><strong>nPlayer (iOS):</strong> iOS ডিভাইসে nPlayer এ খুলবে</li>
                        <li><strong>VLC Mobile:</strong> VLC মোবাইল অ্যাপে খুলবে</li>
                    </ul>
                </div>

                <div class="help-section">
                    <h6><i class="fas fa-desktop"></i> কম্পিউটারে:</h6>
                    <ul>
                        <li><strong>VLC Player:</strong> সবচেয়ে নির্ভরযোগ্য, সব ফরম্যাট সাপোর্ট করে</li>
                        <li><strong>PotPlayer:</strong> উইন্ডোজের জন্য উন্নত ফিচার সহ</li>
                        <li><strong>MPV Player:</strong> দ্রুত ও লাইটওয়েট</li>
                        <li><strong>Kodi:</strong> হোম থিয়েটার সেটআপের জন্য</li>
                    </ul>
                </div>

                <div class="help-section">
                    <h6><i class="fas fa-info-circle"></i> গুরুত্বপূর্ণ তথ্য:</h6>
                    <ul>
                        <li>প্রথমে আপনার পছন্দের প্লেয়ার ইনস্টল করুন</li>
                        <li>যদি সরাসরি না খুলে, ডাইরেক্ট লিংক কপি করে ম্যানুয়ালি প্লেয়ারে পেস্ট করুন</li>
                        <li>ভাল ইন্টারনেট সংযোগ প্রয়োজন</li>
                        <li>কিছু প্লেয়ারে সাবটাইটেল আলাদাভাবে লোড করতে হতে পারে</li>
                    </ul>
                </div>
            </div>
        `;

        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'প্লেয়ার সাহায্য',
                html: helpContent,
                width: '600px',
                showConfirmButton: true,
                confirmButtonText: 'বুঝেছি',
                customClass: {
                    popup: 'player-help-popup'
                }
            });
        } else {
            alert('সাহায্যের জন্য SweetAlert2 প্রয়োজন।');
        }
    }

    // Copy text to clipboard helper function
    function copyToClipboard(text) {
        try {
            navigator.clipboard.writeText(text).then(function() {
                if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        title: 'কপি হয়েছে!',
                        text: 'লিংক ক্লিপবোর্ডে কপি হয়েছে',
                        icon: 'success',
                        timer: 1500,
                        showConfirmButton: false
                    });
                } else {
                    alert('লিংক কপি হয়েছে!');
                }
            }).catch(function() {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        title: 'কপি হয়েছে!',
                        text: 'লিংক ক্লিপবোর্ডে কপি হয়েছে',
                        icon: 'success',
                        timer: 1500,
                        showConfirmButton: false
                    });
                } else {
                    alert('লিংক কপি হয়েছে!');
                }
            });
        } catch (err) {
            alert('লিংক কপি করতে সমস্যা হয়েছে। ম্যানুয়ালি কপি করুন।');
        }
    }

    // Copy direct link to clipboard
    function copyDirectLink() {
        const linkInput = document.getElementById('directLink');
        linkInput.select();
        linkInput.setSelectionRange(0, 99999); // For mobile devices

        try {
            document.execCommand('copy');

            // Show success message
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: 'সফল!',
                    text: 'লিংক কপি হয়েছে!',
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                });
            } else {
                alert('লিংক কপি হয়েছে!');
            }
        } catch (err) {
            // Fallback for modern browsers
            navigator.clipboard.writeText(currentVideoUrl).then(function() {
                if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        title: 'সফল!',
                        text: 'লিংক কপি হয়েছে!',
                        icon: 'success',
                        timer: 1500,
                        showConfirmButton: false
                    });
                } else {
                    alert('লিংক কপি হয়েছে!');
                }
            }).catch(function() {
                alert('লিংক কপি করতে সমস্যা হয়েছে। ম্যানুয়ালি কপি করুন।');
            });
        }
    }

    // Play trailer functionality
    function playTrailer(trailerUrl) {
        // Create modal element
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'dynamicTrailerModal';
        modal.tabIndex = '-1';
        modal.setAttribute('aria-hidden', 'true');

        modal.innerHTML = `
            <div class="modal-dialog modal-dialog-centered modal-lg">
                <div class="modal-content bg-dark">
                    <div class="modal-header border-0">
                        <h5 class="modal-title text-white" id="trailerModalLabel">Movie Trailer</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body p-0">
                        <div class="ratio ratio-16x9">
                            <iframe src="${trailerUrl}" allowfullscreen></iframe>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Initialize and show the modal
        const trailerModal = new bootstrap.Modal(modal);
        trailerModal.show();

        // Remove modal from DOM when hidden
        modal.addEventListener('hidden.bs.modal', function() {
            document.body.removeChild(modal);
        });
    }

    // Initialize similar movies carousel
    document.addEventListener('DOMContentLoaded', function() {
        // Similar movies carousel navigation
        const similarCarousel = document.querySelector('.similar-movies-carousel');
        const prevButton = document.getElementById('similar-prev');
        const nextButton = document.getElementById('similar-next');

        if (similarCarousel && prevButton && nextButton) {
            // Calculate scroll amount based on container width
            const scrollAmount = similarCarousel.offsetWidth * 0.8;

            prevButton.addEventListener('click', () => {
                similarCarousel.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
            });

            nextButton.addEventListener('click', () => {
                similarCarousel.scrollBy({ left: scrollAmount, behavior: 'smooth' });
            });
        }

        // Tab functionality
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content-pane');
        const actionButtons = document.querySelectorAll('.movie-actions .btn');

        function switchTab(tabName) {
            // Remove active class from all buttons and contents
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // Add active class to corresponding button
            document.querySelector(`.tab-button[data-tab="${tabName}"]`).classList.add('active');

            // Show corresponding content
            const tabId = tabName + '-tab';
            document.getElementById(tabId).classList.add('active');

            // Scroll to tab content
            document.querySelector('.custom-tabs').scrollIntoView({ behavior: 'smooth', block: 'start' });
        }

        // Tab button click event
        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                const tabName = this.getAttribute('data-tab');
                switchTab(tabName);
            });
        });

        // Action buttons in movie info section
        actionButtons.forEach(button => {
            button.addEventListener('click', function() {
                const tabName = this.getAttribute('data-tab');
                // Check if the tab exists and is visible
                const tabButton = document.querySelector(`.tab-button[data-tab="${tabName}"]`);
                if (tabName && tabButton) {
                    switchTab(tabName);
                } else if (tabName === 'stream' && !tabButton) {
                    // If stream tab doesn't exist, switch to download tab
                    switchTab('download');
                }
            });
        });

        // Download modal functionality
        const downloadModal = new bootstrap.Modal(document.getElementById('downloadModal'));

        // Submit review form
        const reviewForm = document.getElementById('reviewForm');
        if (reviewForm) {
            reviewForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const formDataObj = {};
                formData.forEach((value, key) => {
                    formDataObj[key] = value;
                });

                // Show loading state
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Submitting...';
                submitBtn.disabled = true;

                // Send AJAX request to submit review
                fetch('<?php echo SITE_URL; ?>/ajax/reviews.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams(formDataObj).toString()
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message with sweetalert if available, otherwise use alert
                        if (typeof Swal !== 'undefined') {
                            Swal.fire({
                                title: 'Success!',
                                text: 'Your review has been submitted successfully.',
                                icon: 'success',
                                confirmButtonText: 'OK'
                            }).then(() => {
                                location.reload();
                            });
                        } else {
                            alert('Review submitted successfully!');
                            location.reload();
                        }
                    } else {
                        // Show error message
                        if (typeof Swal !== 'undefined') {
                            Swal.fire({
                                title: 'Error!',
                                text: data.message || 'An error occurred while submitting your review.',
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        } else {
                            alert(data.message || 'An error occurred while submitting your review.');
                        }

                        // Reset button state
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);

                    // Show error message
                    if (typeof Swal !== 'undefined') {
                        Swal.fire({
                            title: 'Error!',
                            text: 'An error occurred while submitting your review. Please try again.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    } else {
                        alert('An error occurred while submitting your review. Please try again.');
                    }

                    // Reset button state
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                });
            });
        }

        // Add hover effect to download cards
        const downloadCards = document.querySelectorAll('.download-card');
        downloadCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.querySelector('.download-card-action').classList.add('pulse');
            });
            card.addEventListener('mouseleave', function() {
                this.querySelector('.download-card-action').classList.remove('pulse');
            });
        });

        // Make player responsive
        function adjustPlayerSize() {
            const playerWrapper = document.querySelector('.player-wrapper');
            if (playerWrapper) {
                // Maintain 16:9 aspect ratio
                const width = playerWrapper.offsetWidth;
                const height = width * 0.5625; // 9/16 = 0.5625

                // Apply minimum height for very small screens
                const minHeight = 200;
                playerWrapper.style.height = Math.max(height, minHeight) + 'px';
                playerWrapper.style.paddingBottom = '0';
            }
        }

        // Adjust player size on load and resize
        window.addEventListener('load', adjustPlayerSize);
        window.addEventListener('resize', adjustPlayerSize);

        // Also adjust when switching to stream tab
        const streamTabButton = document.querySelector('.tab-button[data-tab="stream"]');
        if (streamTabButton) {
            streamTabButton.addEventListener('click', function() {
                // Small delay to ensure tab content is visible
                setTimeout(adjustPlayerSize, 100);
            });
        }

        // Also adjust when clicking Watch Movie button
        const watchButton = document.querySelector('.btn-watch');
        if (watchButton) {
            watchButton.addEventListener('click', function() {
                // Small delay to ensure tab content is visible
                setTimeout(adjustPlayerSize, 100);
            });
        }

        // Make similar movie cards fully clickable
        const similarMovieCards = document.querySelectorAll('.movie-card');
        similarMovieCards.forEach(card => {
            card.addEventListener('click', function(e) {
                // Don't trigger if clicking on a button or link inside the card
                if (!e.target.closest('.movie-card-btn') && !e.target.closest('a:not(.movie-card-link)')) {
                    const cardLink = this.closest('.movie-card-link');
                    if (cardLink) {
                        window.location.href = cardLink.href;
                    }
                }
            });
        });

        // Handle trailer buttons in similar movies section
        const trailerButtons = document.querySelectorAll('.play-trailer');
        trailerButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const trailerUrl = this.getAttribute('data-trailer');
                playTrailer(trailerUrl);
            });
        });
    });
</script>

<style>
    /* Animation for download button */
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.2); }
        100% { transform: scale(1); }
    }

    .pulse {
        animation: pulse 1s infinite;
    }

    /* Action buttons in movie info section */
    .movie-actions {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
    }

    .btn-watch, .btn-download {
        padding: 12px 25px;
        border-radius: 30px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 10px;
        transition: all 0.3s ease;
    }

    .btn-watch {
        background: linear-gradient(135deg, #4a00e0, #8e2de2);
        color: white;
        border: none;
        box-shadow: 0 5px 15px rgba(142, 45, 226, 0.3);
    }

    .btn-watch:hover {
        background: linear-gradient(135deg, #5a17f3, #9d45e2);
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(142, 45, 226, 0.4);
        color: white;
    }

    .btn-download {
        background: linear-gradient(135deg, #ff7b00, #ff0062);
        color: white;
        border: none;
        box-shadow: 0 5px 15px rgba(255, 0, 98, 0.3);
    }

    .btn-download:hover {
        background: linear-gradient(135deg, #ff8b20, #ff2072);
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(255, 0, 98, 0.4);
        color: white;
    }

    /* Download link container for download and play buttons */
    .download-link-container {
        display: flex;
        width: 100%;
    }

    .download-link-container .download-card,
    .download-link-container .download-link {
        flex: 1;
        width: auto;
        border-radius: 8px 0 0 8px;
    }

    /* Watch Online button styles */
    .watch-online-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #4a00e0;
        color: white;
        padding: 10px 15px;
        border-radius: 0 8px 8px 0;
        text-decoration: none;
        transition: all 0.3s ease;
        font-weight: 600;
        min-width: 80px;
    }

    .watch-online-btn:hover {
        background-color: #5a17f3;
        color: white;
    }

    .watch-online-btn i {
        margin-right: 5px;
    }

    /* Download size and server badges */
    .download-size, .download-server {
        display: inline-block;
        background-color: rgba(0, 0, 0, 0.3);
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        margin-top: 5px;
        margin-right: 5px;
    }

    .download-server {
        background-color: rgba(0, 123, 255, 0.3);
        color: #0dcaf0;
    }

    /* Stream info alert */
    .stream-info .alert {
        background-color: rgba(13, 202, 240, 0.1);
        border-color: rgba(13, 202, 240, 0.2);
        color: #0dcaf0;
        border-radius: 10px;
    }

    .stream-info .alert i {
        margin-right: 8px;
    }

    /* Additional responsive player styles */
    @media (max-width: 767.98px) {
        .player-wrapper {
            min-height: 250px;
        }
    }

    @media (max-width: 575.98px) {
        .player-wrapper {
            min-height: 200px;
        }
    }

    /* Telegram Join Button Styles */
    .btn-telegram {
        background: linear-gradient(135deg, #0088cc, #0056a3);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 30px;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 10px;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(0, 136, 204, 0.3);
    }

    .btn-telegram:hover {
        background: linear-gradient(135deg, #0099dd, #0067b4);
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(0, 136, 204, 0.4);
        color: white;
    }

    .btn-telegram i {
        font-size: 1.2rem;
    }
</style>