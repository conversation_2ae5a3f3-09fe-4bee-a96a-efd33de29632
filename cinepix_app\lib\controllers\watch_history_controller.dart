import 'dart:convert';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cinepix_app/models/watch_history_item.dart';

class WatchHistoryController extends GetxController {
  static const String _watchHistoryKey = 'watch_history';
  static const int _maxHistoryItems = 10;

  final RxList<WatchHistoryItem> watchHistory = <WatchHistoryItem>[].obs;
  final RxBool isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    loadWatchHistory();
  }

  // Load watch history from SharedPreferences
  Future<void> loadWatchHistory() async {
    isLoading.value = true;

    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString(_watchHistoryKey);

      if (historyJson != null) {
        final List<dynamic> historyList = json.decode(historyJson);
        final items =
            historyList.map((item) => WatchHistoryItem.fromJson(item)).toList();

        // Sort by last watched time (newest first)
        items.sort((a, b) => b.lastWatchedAt.compareTo(a.lastWatchedAt));

        watchHistory.value = items;
      }
    } catch (e) {
      print('Error loading watch history: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Save watch history to SharedPreferences
  Future<void> _saveWatchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson =
          json.encode(watchHistory.map((item) => item.toJson()).toList());
      await prefs.setString(_watchHistoryKey, historyJson);
    } catch (e) {
      print('Error saving watch history: $e');
    }
  }

  // Add or update item in watch history
  Future<void> updateWatchHistory({
    required String contentType,
    required int contentId,
    int parentId = 0, // Added parentId for episodes (tvshow_id)
    required String title,
    required String posterUrl,
    String backdropUrl = '',
    required int duration,
    required int watchedPosition,
    int? seasonNumber, // Season number for episodes
    int? episodeNumber, // Episode number for episodes
  }) async {
    if (watchedPosition < 30 || watchedPosition >= duration - 30) {
      // Don't save if less than 30 seconds watched or if it's almost complete
      return;
    }

    // Calculate progress percentage
    final progress = (watchedPosition / duration) * 100;

    // For episodes, check if there's already an entry for the same TV show
    // and replace it instead of adding multiple episodes
    int existingIndex = -1;

    if (contentType == 'episode' && parentId > 0) {
      // Find existing episode from the same TV show
      existingIndex = watchHistory.indexWhere(
          (item) => item.contentType == 'episode' && item.parentId == parentId);
    } else {
      // For movies, check by contentId
      existingIndex = watchHistory.indexWhere((item) =>
          item.contentType == contentType && item.contentId == contentId);
    }

    if (existingIndex >= 0) {
      // Update existing item
      final updatedItem = WatchHistoryItem(
        id: watchHistory[existingIndex].id,
        contentType: contentType,
        contentId: contentId,
        parentId: parentId, // Use provided parentId
        title: title,
        // Keep existing poster URL if the new one is empty or null
        posterUrl: (posterUrl.isEmpty || posterUrl.contains('null'))
            ? watchHistory[existingIndex].posterUrl
            : posterUrl,
        // Keep existing backdrop URL if the new one is empty or null
        backdropUrl: (backdropUrl.isEmpty || backdropUrl.contains('null'))
            ? watchHistory[existingIndex].backdropUrl
            : backdropUrl,
        duration: duration,
        watchedPosition: watchedPosition,
        progress: progress,
        lastWatchedAt: DateTime.now(),
        seasonNumber: seasonNumber,
        episodeNumber: episodeNumber,
      );

      watchHistory[existingIndex] = updatedItem;
    } else {
      // Add new item
      final newItem = WatchHistoryItem(
        id: DateTime.now().millisecondsSinceEpoch,
        contentType: contentType,
        contentId: contentId,
        parentId: parentId, // Use provided parentId
        title: title,
        // Make sure we have a valid poster URL
        posterUrl:
            (posterUrl.isEmpty || posterUrl.contains('null')) ? '' : posterUrl,
        // Use backdrop URL if available
        backdropUrl: backdropUrl,
        duration: duration,
        watchedPosition: watchedPosition,
        progress: progress,
        lastWatchedAt: DateTime.now(),
        seasonNumber: seasonNumber,
        episodeNumber: episodeNumber,
      );

      watchHistory.insert(0, newItem);

      // Limit the number of items in history
      if (watchHistory.length > _maxHistoryItems) {
        watchHistory.removeLast();
      }
    }

    // Sort by last watched time (newest first)
    watchHistory.sort((a, b) => b.lastWatchedAt.compareTo(a.lastWatchedAt));

    // Save to SharedPreferences
    await _saveWatchHistory();

    // Force UI refresh
    watchHistory.refresh();
  }

  // Remove item from watch history
  Future<void> removeFromHistory(int id) async {
    try {
      // Find the index of the item to be removed
      final index = watchHistory.indexWhere((item) => item.id == id);

      if (index != -1) {
        // Remove the item from the list immediately for instant UI update
        watchHistory.removeAt(index);

        // Force UI refresh immediately
        watchHistory.refresh();

        // Save changes to SharedPreferences in background
        await _saveWatchHistory();

        print('Item removed from watch history successfully');
      } else {
        print('Item not found in watch history');
      }
    } catch (e) {
      print('Error removing item from watch history: $e');
      // Reload history in case of error
      await loadWatchHistory();
    }
  }

  // Clear watch history
  Future<void> clearHistory() async {
    watchHistory.clear();
    await _saveWatchHistory();
  }

  // Get watch position for content
  int getWatchPosition(String contentType, int contentId) {
    try {
      final item = watchHistory.firstWhere((item) =>
          item.contentType == contentType && item.contentId == contentId);
      return item.watchedPosition;
    } catch (e) {
      return 0;
    }
  }
}
