<?php
require_once 'includes/config.php';
require_once 'includes/auth.php';

// Check if user is logged in and is admin (you can modify this based on your auth system)
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$page_title = "শর্টলিংক ম্যানেজার";
include 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0"><i class="fas fa-link"></i> শর্টলিংক ম্যানেজার</h4>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createLinkModal">
                        <i class="fas fa-plus"></i> নতুন লিংক তৈরি করুন
                    </button>
                </div>
                <div class="card-body">
                    <!-- Search and Filter -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <input type="text" id="searchLinks" class="form-control" placeholder="লিংক খুঁজুন...">
                        </div>
                        <div class="col-md-3">
                            <select id="statusFilter" class="form-select">
                                <option value="">সব স্ট্যাটাস</option>
                                <option value="1">সক্রিয়</option>
                                <option value="0">নিষ্ক্রিয়</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-secondary" onclick="loadLinks()">
                                <i class="fas fa-refresh"></i> রিফ্রেশ
                            </button>
                        </div>
                    </div>

                    <!-- Links Table -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>শর্ট কোড</th>
                                    <th>টাইটেল</th>
                                    <th>অরিজিনাল URL</th>
                                    <th>ক্লিক</th>
                                    <th>স্ট্যাটাস</th>
                                    <th>তৈরি হয়েছে</th>
                                    <th>অ্যাকশন</th>
                                </tr>
                            </thead>
                            <tbody id="linksTableBody">
                                <tr>
                                    <td colspan="7" class="text-center">
                                        <div class="spinner-border" role="status">
                                            <span class="visually-hidden">লোড হচ্ছে...</span>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center" id="pagination">
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Link Modal -->
<div class="modal fade" id="createLinkModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">নতুন শর্টলিংক তৈরি করুন</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createLinkForm">
                    <div class="mb-3">
                        <label for="originalUrl" class="form-label">অরিজিনাল URL *</label>
                        <input type="url" class="form-control" id="originalUrl" required>
                    </div>
                    <div class="mb-3">
                        <label for="linkTitle" class="form-label">টাইটেল</label>
                        <input type="text" class="form-control" id="linkTitle">
                    </div>
                    <div class="mb-3">
                        <label for="linkDescription" class="form-label">বিবরণ</label>
                        <textarea class="form-control" id="linkDescription" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="customCode" class="form-label">কাস্টম কোড (ঐচ্ছিক)</label>
                        <input type="text" class="form-control" id="customCode" placeholder="3-10 অক্ষর">
                        <div class="form-text">খালি রাখলে অটোমেটিক কোড তৈরি হবে</div>
                    </div>
                    <div class="mb-3">
                        <label for="expiresAt" class="form-label">মেয়াদ শেষ (ঐচ্ছিক)</label>
                        <input type="datetime-local" class="form-control" id="expiresAt">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                <button type="button" class="btn btn-primary" onclick="createLink()">তৈরি করুন</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Link Modal -->
<div class="modal fade" id="editLinkModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">শর্টলিংক এডিট করুন</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editLinkForm">
                    <input type="hidden" id="editShortCode">
                    <div class="mb-3">
                        <label for="editLinkTitle" class="form-label">টাইটেল</label>
                        <input type="text" class="form-control" id="editLinkTitle">
                    </div>
                    <div class="mb-3">
                        <label for="editLinkDescription" class="form-label">বিবরণ</label>
                        <textarea class="form-control" id="editLinkDescription" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editIsActive">
                            <label class="form-check-label" for="editIsActive">সক্রিয়</label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="editExpiresAt" class="form-label">মেয়াদ শেষ</label>
                        <input type="datetime-local" class="form-control" id="editExpiresAt">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                <button type="button" class="btn btn-primary" onclick="updateLink()">আপডেট করুন</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentPage = 1;
const itemsPerPage = 20;

// Load links on page load
document.addEventListener('DOMContentLoaded', function() {
    loadLinks();
});

// Load links function
function loadLinks(page = 1) {
    currentPage = page;
    const search = document.getElementById('searchLinks').value;
    const status = document.getElementById('statusFilter').value;
    
    fetch(`api/shortlink.php?page=${page}&limit=${itemsPerPage}`)
        .then(response => response.json())
        .then(data => {
            if (data.links) {
                displayLinks(data.links);
                displayPagination(data.pagination);
            }
        })
        .catch(error => {
            console.error('Error loading links:', error);
            showAlert('লিংক লোড করতে সমস্যা হয়েছে', 'danger');
        });
}

// Display links in table
function displayLinks(links) {
    const tbody = document.getElementById('linksTableBody');
    
    if (links.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center">কোন লিংক পাওয়া যায়নি</td></tr>';
        return;
    }
    
    tbody.innerHTML = links.map(link => `
        <tr>
            <td>
                <code>${link.short_code}</code>
                <button class="btn btn-sm btn-outline-secondary ms-1" onclick="copyToClipboard('${link.short_url}')" title="কপি করুন">
                    <i class="fas fa-copy"></i>
                </button>
            </td>
            <td>${link.title || '-'}</td>
            <td>
                <a href="${link.original_url}" target="_blank" class="text-truncate d-inline-block" style="max-width: 200px;">
                    ${link.original_url}
                </a>
            </td>
            <td><span class="badge bg-info">${link.clicks}</span></td>
            <td>
                <span class="badge ${link.is_active ? 'bg-success' : 'bg-secondary'}">
                    ${link.is_active ? 'সক্রিয়' : 'নিষ্ক্রিয়'}
                </span>
            </td>
            <td>${new Date(link.created_at).toLocaleDateString('bn-BD')}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="editLink('${link.short_code}')" title="এডিট">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger ms-1" onclick="deleteLink('${link.short_code}')" title="ডিলিট">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

// Display pagination
function displayPagination(pagination) {
    const paginationEl = document.getElementById('pagination');
    
    if (pagination.pages <= 1) {
        paginationEl.innerHTML = '';
        return;
    }
    
    let html = '';
    
    // Previous button
    if (pagination.page > 1) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadLinks(${pagination.page - 1})">পূর্ববর্তী</a></li>`;
    }
    
    // Page numbers
    for (let i = 1; i <= pagination.pages; i++) {
        if (i === pagination.page) {
            html += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
        } else {
            html += `<li class="page-item"><a class="page-link" href="#" onclick="loadLinks(${i})">${i}</a></li>`;
        }
    }
    
    // Next button
    if (pagination.page < pagination.pages) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadLinks(${pagination.page + 1})">পরবর্তী</a></li>`;
    }
    
    paginationEl.innerHTML = html;
}

// Create new link
function createLink() {
    const formData = {
        url: document.getElementById('originalUrl').value,
        title: document.getElementById('linkTitle').value,
        description: document.getElementById('linkDescription').value,
        custom_code: document.getElementById('customCode').value,
        expires_at: document.getElementById('expiresAt').value || null
    };
    
    if (!formData.url) {
        showAlert('URL প্রয়োজন', 'danger');
        return;
    }
    
    fetch('api/shortlink.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(`শর্টলিংক তৈরি হয়েছে: ${data.short_url}`, 'success');
            document.getElementById('createLinkForm').reset();
            bootstrap.Modal.getInstance(document.getElementById('createLinkModal')).hide();
            loadLinks();
        } else {
            showAlert(data.error || 'লিংক তৈরি করতে সমস্যা হয়েছে', 'danger');
        }
    })
    .catch(error => {
        console.error('Error creating link:', error);
        showAlert('লিংক তৈরি করতে সমস্যা হয়েছে', 'danger');
    });
}

// Edit link
function editLink(shortCode) {
    fetch(`api/shortlink.php?code=${shortCode}`)
        .then(response => response.json())
        .then(data => {
            if (data.short_code) {
                document.getElementById('editShortCode').value = data.short_code;
                document.getElementById('editLinkTitle').value = data.title || '';
                document.getElementById('editLinkDescription').value = data.description || '';
                document.getElementById('editIsActive').checked = data.is_active;
                document.getElementById('editExpiresAt').value = data.expires_at ? data.expires_at.slice(0, 16) : '';
                
                new bootstrap.Modal(document.getElementById('editLinkModal')).show();
            } else {
                showAlert('লিংক পাওয়া যায়নি', 'danger');
            }
        })
        .catch(error => {
            console.error('Error loading link:', error);
            showAlert('লিংক লোড করতে সমস্যা হয়েছে', 'danger');
        });
}

// Update link
function updateLink() {
    const formData = {
        code: document.getElementById('editShortCode').value,
        title: document.getElementById('editLinkTitle').value,
        description: document.getElementById('editLinkDescription').value,
        is_active: document.getElementById('editIsActive').checked,
        expires_at: document.getElementById('editExpiresAt').value || null
    };
    
    fetch('api/shortlink.php', {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('লিংক আপডেট হয়েছে', 'success');
            bootstrap.Modal.getInstance(document.getElementById('editLinkModal')).hide();
            loadLinks();
        } else {
            showAlert(data.error || 'লিংক আপডেট করতে সমস্যা হয়েছে', 'danger');
        }
    })
    .catch(error => {
        console.error('Error updating link:', error);
        showAlert('লিংক আপডেট করতে সমস্যা হয়েছে', 'danger');
    });
}

// Delete link
function deleteLink(shortCode) {
    if (!confirm('আপনি কি নিশ্চিত যে এই লিংকটি ডিলিট করতে চান?')) {
        return;
    }
    
    fetch('api/shortlink.php', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ code: shortCode })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('লিংক ডিলিট হয়েছে', 'success');
            loadLinks();
        } else {
            showAlert(data.error || 'লিংক ডিলিট করতে সমস্যা হয়েছে', 'danger');
        }
    })
    .catch(error => {
        console.error('Error deleting link:', error);
        showAlert('লিংক ডিলিট করতে সমস্যা হয়েছে', 'danger');
    });
}

// Copy to clipboard
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showAlert('লিংক কপি হয়েছে', 'success');
    }).catch(() => {
        showAlert('কপি করতে সমস্যা হয়েছে', 'danger');
    });
}

// Show alert
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

// Search functionality
document.getElementById('searchLinks').addEventListener('input', function() {
    // Implement search functionality if needed
});

document.getElementById('statusFilter').addEventListener('change', function() {
    // Implement filter functionality if needed
});
</script>

<?php include 'includes/footer.php'; ?>
