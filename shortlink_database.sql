-- Short Link System Database Table
-- Run this SQL to create the shortlink table

CREATE TABLE IF NOT EXISTS shortlinks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    short_code VARCHAR(10) NOT NULL UNIQUE,
    original_url TEXT NOT NULL,
    title VARCHAR(255) DEFAULT NULL,
    description TEXT DEFAULT NULL,
    clicks INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    expires_at DATETIME DEFAULT NULL,
    created_by INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_short_code (short_code),
    INDEX idx_created_by (created_by),
    INDEX idx_active (is_active),
    INDEX idx_expires (expires_at)
);

-- Insert some sample data
INSERT INTO shortlinks (short_code, original_url, title, description, clicks) VALUES
('abc123', 'https://cinepix.top/movie/the-matrix-1999-1', 'The Matrix Movie', 'Watch The Matrix movie online', 0),
('xyz789', 'https://cinepix.top/tv-show/breaking-bad-2008-1', 'Breaking Bad Series', 'Watch Breaking Bad TV series', 0);
