<?php
/**
 * CinePix Short Link System
 * Simple URL shortener for the website
 */

require_once 'includes/config.php';

// Get the short code from URL
$short_code = isset($_GET['code']) ? trim($_GET['code']) : '';

if (empty($short_code)) {
    // If no code provided, redirect to homepage
    header('Location: ' . SITE_URL);
    exit;
}

// Validate short code format (alphanumeric, 3-10 characters)
if (!preg_match('/^[a-zA-Z0-9]{3,10}$/', $short_code)) {
    header('HTTP/1.0 404 Not Found');
    include '404.php';
    exit;
}

// Look up the short code in database
$stmt = $conn->prepare("SELECT original_url, title, is_active, expires_at FROM shortlinks WHERE short_code = ?");
$stmt->bind_param("s", $short_code);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    // Short code not found
    header('HTTP/1.0 404 Not Found');
    include '404.php';
    exit;
}

$link = $result->fetch_assoc();

// Check if link is active
if (!$link['is_active']) {
    header('HTTP/1.0 404 Not Found');
    include '404.php';
    exit;
}

// Check if link has expired
if ($link['expires_at'] && strtotime($link['expires_at']) < time()) {
    header('HTTP/1.0 404 Not Found');
    include '404.php';
    exit;
}

// Update click count
$update_stmt = $conn->prepare("UPDATE shortlinks SET clicks = clicks + 1 WHERE short_code = ?");
$update_stmt->bind_param("s", $short_code);
$update_stmt->execute();

// Get user's IP for logging (optional)
$user_ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
$user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

// Log the click (optional - you can create a clicks table later if needed)
// For now, we'll just redirect

// Redirect to original URL
header('Location: ' . $link['original_url']);
exit;
?>
