<?php
/**
 * Short Link Helper Functions
 * Utility functions for creating and managing short links
 */

/**
 * Generate a random short code
 */
function generateShortCode($length = 6) {
    $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $code = '';
    for ($i = 0; $i < $length; $i++) {
        $code .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $code;
}

/**
 * Check if a short code already exists
 */
function shortCodeExists($conn, $code) {
    $stmt = $conn->prepare("SELECT id FROM shortlinks WHERE short_code = ?");
    $stmt->bind_param("s", $code);
    $stmt->execute();
    return $stmt->get_result()->num_rows > 0;
}

/**
 * Create a new short link
 */
function createShortLink($conn, $original_url, $title = null, $description = null, $custom_code = null, $expires_at = null) {
    // Validate URL
    if (!filter_var($original_url, FILTER_VALIDATE_URL)) {
        return ['success' => false, 'error' => 'Invalid URL format'];
    }
    
    // Generate or validate custom code
    if (!empty($custom_code)) {
        // Validate custom code format
        if (!preg_match('/^[a-zA-Z0-9]{3,10}$/', $custom_code)) {
            return ['success' => false, 'error' => 'Custom code must be 3-10 alphanumeric characters'];
        }
        
        // Check if custom code exists
        if (shortCodeExists($conn, $custom_code)) {
            return ['success' => false, 'error' => 'Custom code already exists'];
        }
        
        $short_code = $custom_code;
    } else {
        // Generate unique short code
        do {
            $short_code = generateShortCode();
        } while (shortCodeExists($conn, $short_code));
    }
    
    // Insert into database
    $stmt = $conn->prepare("INSERT INTO shortlinks (short_code, original_url, title, description, expires_at) VALUES (?, ?, ?, ?, ?)");
    $stmt->bind_param("sssss", $short_code, $original_url, $title, $description, $expires_at);
    
    if ($stmt->execute()) {
        $short_url = SITE_URL . '/s/' . $short_code;
        return [
            'success' => true,
            'short_code' => $short_code,
            'short_url' => $short_url,
            'original_url' => $original_url,
            'title' => $title
        ];
    } else {
        return ['success' => false, 'error' => 'Failed to create short link'];
    }
}

/**
 * Get short link information
 */
function getShortLink($conn, $short_code) {
    $stmt = $conn->prepare("SELECT * FROM shortlinks WHERE short_code = ?");
    $stmt->bind_param("s", $short_code);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $link = $result->fetch_assoc();
        $link['short_url'] = SITE_URL . '/s/' . $link['short_code'];
        return $link;
    }
    
    return null;
}

/**
 * Update short link
 */
function updateShortLink($conn, $short_code, $updates) {
    $allowed_fields = ['title', 'description', 'is_active', 'expires_at'];
    $set_clauses = [];
    $params = [];
    $types = '';
    
    foreach ($updates as $field => $value) {
        if (in_array($field, $allowed_fields)) {
            $set_clauses[] = "$field = ?";
            $params[] = $value;
            $types .= is_bool($value) ? 'i' : 's';
        }
    }
    
    if (empty($set_clauses)) {
        return ['success' => false, 'error' => 'No valid fields to update'];
    }
    
    $params[] = $short_code;
    $types .= 's';
    
    $sql = "UPDATE shortlinks SET " . implode(', ', $set_clauses) . " WHERE short_code = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    
    if ($stmt->execute() && $stmt->affected_rows > 0) {
        return ['success' => true, 'message' => 'Short link updated'];
    } else {
        return ['success' => false, 'error' => 'Short link not found or no changes made'];
    }
}

/**
 * Delete short link
 */
function deleteShortLink($conn, $short_code) {
    $stmt = $conn->prepare("DELETE FROM shortlinks WHERE short_code = ?");
    $stmt->bind_param("s", $short_code);
    
    if ($stmt->execute() && $stmt->affected_rows > 0) {
        return ['success' => true, 'message' => 'Short link deleted'];
    } else {
        return ['success' => false, 'error' => 'Short link not found'];
    }
}

/**
 * Get short link statistics
 */
function getShortLinkStats($conn) {
    $stats_query = "SELECT 
        COUNT(*) as total_links,
        SUM(clicks) as total_clicks,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_links,
        COUNT(CASE WHEN is_active = 0 THEN 1 END) as inactive_links,
        COUNT(CASE WHEN expires_at IS NOT NULL AND expires_at < NOW() THEN 1 END) as expired_links,
        AVG(clicks) as avg_clicks
    FROM shortlinks";
    
    $result = $conn->query($stats_query);
    return $result->fetch_assoc();
}

/**
 * Get top clicked short links
 */
function getTopClickedLinks($conn, $limit = 10) {
    $stmt = $conn->prepare("SELECT short_code, original_url, title, clicks, created_at 
                           FROM shortlinks 
                           WHERE clicks > 0 
                           ORDER BY clicks DESC 
                           LIMIT ?");
    $stmt->bind_param("i", $limit);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $links = [];
    while ($row = $result->fetch_assoc()) {
        $row['short_url'] = SITE_URL . '/s/' . $row['short_code'];
        $links[] = $row;
    }
    
    return $links;
}

/**
 * Get recent short links
 */
function getRecentLinks($conn, $limit = 10) {
    $stmt = $conn->prepare("SELECT short_code, original_url, title, clicks, created_at 
                           FROM shortlinks 
                           ORDER BY created_at DESC 
                           LIMIT ?");
    $stmt->bind_param("i", $limit);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $links = [];
    while ($row = $result->fetch_assoc()) {
        $row['short_url'] = SITE_URL . '/s/' . $row['short_code'];
        $links[] = $row;
    }
    
    return $links;
}

/**
 * Increment click count for a short link
 */
function incrementClickCount($conn, $short_code) {
    $stmt = $conn->prepare("UPDATE shortlinks SET clicks = clicks + 1 WHERE short_code = ?");
    $stmt->bind_param("s", $short_code);
    return $stmt->execute();
}

/**
 * Check if short link is valid and active
 */
function isShortLinkValid($conn, $short_code) {
    $stmt = $conn->prepare("SELECT is_active, expires_at FROM shortlinks WHERE short_code = ?");
    $stmt->bind_param("s", $short_code);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        return false; // Link doesn't exist
    }
    
    $link = $result->fetch_assoc();
    
    // Check if link is active
    if (!$link['is_active']) {
        return false;
    }
    
    // Check if link has expired
    if ($link['expires_at'] && strtotime($link['expires_at']) < time()) {
        return false;
    }
    
    return true;
}

/**
 * Generate short link for movie
 */
function createMovieShortLink($conn, $movie_id, $movie_title, $year) {
    $original_url = SITE_URL . "/movie/" . generateSlug($movie_title) . "-" . $year . "-" . $movie_id;
    $title = "Watch " . $movie_title . " (" . $year . ")";
    $description = "Watch " . $movie_title . " movie online on CinePix";
    
    return createShortLink($conn, $original_url, $title, $description);
}

/**
 * Generate short link for TV show
 */
function createTvShowShortLink($conn, $tvshow_id, $tvshow_title, $year) {
    $original_url = SITE_URL . "/tv-show/" . generateSlug($tvshow_title) . "-" . $year . "-" . $tvshow_id;
    $title = "Watch " . $tvshow_title . " (" . $year . ")";
    $description = "Watch " . $tvshow_title . " TV series online on CinePix";
    
    return createShortLink($conn, $original_url, $title, $description);
}

/**
 * Bulk create short links for content
 */
function bulkCreateShortLinks($conn, $content_list, $content_type = 'movie') {
    $results = [];
    
    foreach ($content_list as $content) {
        if ($content_type === 'movie') {
            $result = createMovieShortLink($conn, $content['id'], $content['title'], $content['year']);
        } else {
            $result = createTvShowShortLink($conn, $content['id'], $content['title'], $content['year']);
        }
        
        $results[] = [
            'content_id' => $content['id'],
            'content_title' => $content['title'],
            'result' => $result
        ];
    }
    
    return $results;
}

/**
 * Clean up expired short links
 */
function cleanupExpiredLinks($conn) {
    $stmt = $conn->prepare("DELETE FROM shortlinks WHERE expires_at IS NOT NULL AND expires_at < NOW()");
    $stmt->execute();
    return $stmt->affected_rows;
}

/**
 * Get short link analytics data
 */
function getShortLinkAnalytics($conn, $days = 30) {
    $stmt = $conn->prepare("SELECT
        DATE(created_at) as date,
        COUNT(*) as links_created,
        SUM(clicks) as total_clicks
    FROM shortlinks
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
    GROUP BY DATE(created_at)
    ORDER BY date DESC");

    $stmt->bind_param("i", $days);
    $stmt->execute();
    $result = $stmt->get_result();

    $analytics = [];
    while ($row = $result->fetch_assoc()) {
        $analytics[] = $row;
    }

    return $analytics;
}

/**
 * Generate download redirect URL
 */
function generateDownloadRedirectUrl($download_url, $title, $quality = '', $server = '', $timer = null) {
    $params = [
        'url' => $download_url,
        'title' => $title
    ];

    if (!empty($quality)) {
        $params['quality'] = $quality;
    }

    if (!empty($server)) {
        $params['server'] = $server;
    }

    if ($timer !== null) {
        $params['timer'] = intval($timer);
    }

    return SITE_URL . '/download_redirect.php?' . http_build_query($params);
}

/**
 * Get download settings
 */
function getDownloadSettings($conn) {
    $settings = [
        'download_timer' => 4,
        'premium_timer' => 2,
        'show_ads' => 1,
        'popunder_ads' => 1,
        'ad_delay' => 2
    ];

    $result = $conn->query("SELECT setting_key, setting_value FROM download_settings");

    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
    }

    return $settings;
}
?>
