import 'dart:io';
import 'package:flutter/foundation.dart';

class VideoOptimizationService {
  static final VideoOptimizationService _instance =
      VideoOptimizationService._internal();
  factory VideoOptimizationService() => _instance;
  VideoOptimizationService._internal();

  // Network optimization settings - optimized for faster loading
  static const int _maxConcurrentConnections = 4;
  static const int _connectionTimeoutSeconds = 15; // Reduced timeout
  static const int _readTimeoutSeconds = 30; // Reduced timeout
  static const int _bufferSizeBytes =
      32 * 1024 * 1024; // 32MB - reduced for faster loading

  /// Get optimized HTTP headers for video streaming
  Map<String, String> getOptimizedHeaders({
    String? userAgent,
    String? referer,
    bool enableRangeRequests = true,
  }) {
    final headers = <String, String>{
      'User-Agent': userAgent ?? 'CinePix/2.0 (Enhanced)',
      'Accept': '*/*',
      'Accept-Encoding': 'identity',
      'Connection': 'keep-alive',
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache',
    };

    if (referer != null) {
      headers['Referer'] = referer;
    }

    if (enableRangeRequests) {
      headers['Range'] = 'bytes=0-';
    }

    return headers;
  }

  /// Get optimized media extras for better performance
  Map<String, String> getMediaExtras({
    String? networkTimeout,
    bool enableReconnect = true,
    bool enableHardwareDecoding = true,
  }) {
    final extras = <String, String>{
      'network-timeout': networkTimeout ?? '15', // Reduced timeout
      'cache': 'yes',
      'cache-secs': '60', // Reduced cache time for faster loading
      'demuxer-max-bytes': '50M', // Reduced for faster loading
      'demuxer-max-back-bytes': '25M',
      // Additional performance optimizations
      'prefetch-playlist': 'yes',
      'stream-buffer-size': '8192',
      'hr-seek': 'yes',
      'force-seekable': 'yes',
    };

    if (enableReconnect) {
      extras.addAll({
        'reconnect-streamed': 'yes',
        'reconnect-delay-max': '3', // Reduced delay
        'reconnect-at-eof': 'yes',
      });
    }

    if (enableHardwareDecoding) {
      extras.addAll({
        'hwdec': 'auto',
        'vo': 'gpu',
        'video-sync': 'audio',
        // Additional hardware acceleration
        'gpu-context': 'auto',
        'opengl-pbo': 'yes',
      });
    }

    return extras;
  }

  /// Analyze video URL and suggest optimizations
  VideoOptimizationResult analyzeVideoUrl(String url) {
    final uri = Uri.parse(url);
    final isLocal = url.startsWith('file://');
    final isSecure = uri.scheme == 'https';
    final fileExtension = uri.path.split('.').last.toLowerCase();

    // Determine video quality based on URL patterns
    VideoQuality quality = VideoQuality.unknown;
    if (url.contains('1080p') || url.contains('1920x1080')) {
      quality = VideoQuality.fullHD;
    } else if (url.contains('720p') || url.contains('1280x720')) {
      quality = VideoQuality.hd;
    } else if (url.contains('480p') || url.contains('854x480')) {
      quality = VideoQuality.sd;
    } else if (url.contains('360p') || url.contains('640x360')) {
      quality = VideoQuality.low;
    }

    // Determine container format
    VideoContainer container = VideoContainer.unknown;
    switch (fileExtension) {
      case 'mp4':
        container = VideoContainer.mp4;
        break;
      case 'mkv':
        container = VideoContainer.mkv;
        break;
      case 'avi':
        container = VideoContainer.avi;
        break;
      case 'webm':
        container = VideoContainer.webm;
        break;
      case 'm3u8':
        container = VideoContainer.hls;
        break;
    }

    return VideoOptimizationResult(
      url: url,
      isLocal: isLocal,
      isSecure: isSecure,
      quality: quality,
      container: container,
      suggestedBufferSize: _getSuggestedBufferSize(quality, container),
      suggestedPreloadSize: _getSuggestedPreloadSize(quality),
      requiresSpecialHandling: _requiresSpecialHandling(container),
    );
  }

  int _getSuggestedBufferSize(VideoQuality quality, VideoContainer container) {
    // Base buffer size
    int baseSize = 64 * 1024 * 1024; // 64MB

    // Adjust based on quality
    switch (quality) {
      case VideoQuality.fullHD:
        baseSize = 128 * 1024 * 1024; // 128MB
        break;
      case VideoQuality.hd:
        baseSize = 96 * 1024 * 1024; // 96MB
        break;
      case VideoQuality.sd:
        baseSize = 64 * 1024 * 1024; // 64MB
        break;
      case VideoQuality.low:
        baseSize = 32 * 1024 * 1024; // 32MB
        break;
      case VideoQuality.unknown:
        baseSize = 64 * 1024 * 1024; // Default 64MB
        break;
    }

    // Adjust based on container
    if (container == VideoContainer.mkv) {
      baseSize = (baseSize * 1.5).round(); // MKV files often need more buffer
    }

    return baseSize;
  }

  int _getSuggestedPreloadSize(VideoQuality quality) {
    switch (quality) {
      case VideoQuality.fullHD:
        return 10 * 1024 * 1024; // 10MB
      case VideoQuality.hd:
        return 8 * 1024 * 1024; // 8MB
      case VideoQuality.sd:
        return 5 * 1024 * 1024; // 5MB
      case VideoQuality.low:
        return 3 * 1024 * 1024; // 3MB
      case VideoQuality.unknown:
        return 5 * 1024 * 1024; // Default 5MB
    }
  }

  bool _requiresSpecialHandling(VideoContainer container) {
    return container == VideoContainer.mkv ||
        container == VideoContainer.hls ||
        container == VideoContainer.webm;
  }

  /// Get network-specific optimizations
  NetworkOptimization getNetworkOptimization() {
    // In a real app, you might detect network type and speed
    return NetworkOptimization(
      maxConcurrentConnections: _maxConcurrentConnections,
      connectionTimeout: Duration(seconds: _connectionTimeoutSeconds),
      readTimeout: Duration(seconds: _readTimeoutSeconds),
      enableKeepAlive: true,
      enableCompression: false, // Usually disabled for video
      retryAttempts: 3,
      retryDelay: Duration(seconds: 2),
    );
  }

  /// Check if device supports hardware decoding
  bool supportsHardwareDecoding() {
    if (kIsWeb) return false;

    // On mobile devices, hardware decoding is usually available
    if (Platform.isAndroid || Platform.isIOS) {
      return true;
    }

    // On desktop, it depends on the system
    return Platform.isWindows || Platform.isMacOS || Platform.isLinux;
  }

  /// Get recommended player settings based on device capabilities
  PlayerSettings getRecommendedPlayerSettings() {
    final supportsHW = supportsHardwareDecoding();

    return PlayerSettings(
      enableHardwareDecoding: supportsHW,
      bufferSize: _bufferSizeBytes,
      enableGPURendering: supportsHW,
      audioBufferSize: 2.0, // 2 seconds
      enableHighQualityScaling: supportsHW,
      enableDeinterlacing: true,
      maxDroppedFrames: 5,
    );
  }
}

enum VideoQuality {
  low, // 360p and below
  sd, // 480p
  hd, // 720p
  fullHD, // 1080p
  unknown,
}

enum VideoContainer {
  mp4,
  mkv,
  avi,
  webm,
  hls,
  unknown,
}

class VideoOptimizationResult {
  final String url;
  final bool isLocal;
  final bool isSecure;
  final VideoQuality quality;
  final VideoContainer container;
  final int suggestedBufferSize;
  final int suggestedPreloadSize;
  final bool requiresSpecialHandling;

  VideoOptimizationResult({
    required this.url,
    required this.isLocal,
    required this.isSecure,
    required this.quality,
    required this.container,
    required this.suggestedBufferSize,
    required this.suggestedPreloadSize,
    required this.requiresSpecialHandling,
  });

  @override
  String toString() {
    return 'VideoOptimizationResult(quality: $quality, container: $container, '
        'bufferSize: ${(suggestedBufferSize / 1024 / 1024).toStringAsFixed(1)}MB, '
        'isLocal: $isLocal, requiresSpecialHandling: $requiresSpecialHandling)';
  }
}

class NetworkOptimization {
  final int maxConcurrentConnections;
  final Duration connectionTimeout;
  final Duration readTimeout;
  final bool enableKeepAlive;
  final bool enableCompression;
  final int retryAttempts;
  final Duration retryDelay;

  NetworkOptimization({
    required this.maxConcurrentConnections,
    required this.connectionTimeout,
    required this.readTimeout,
    required this.enableKeepAlive,
    required this.enableCompression,
    required this.retryAttempts,
    required this.retryDelay,
  });
}

class PlayerSettings {
  final bool enableHardwareDecoding;
  final int bufferSize;
  final bool enableGPURendering;
  final double audioBufferSize;
  final bool enableHighQualityScaling;
  final bool enableDeinterlacing;
  final int maxDroppedFrames;

  PlayerSettings({
    required this.enableHardwareDecoding,
    required this.bufferSize,
    required this.enableGPURendering,
    required this.audioBufferSize,
    required this.enableHighQualityScaling,
    required this.enableDeinterlacing,
    required this.maxDroppedFrames,
  });
}
